// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: msgid.proto

package commonPB

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MsgID 消息 ID
type MsgID int32

const (
	// ********************************************************************************
	MsgID_CMD_BEGIN MsgID = 0
	// ********************************************************************************
	MsgID_CMD_GATE_BEGIN         MsgID = 1000
	MsgID_GATE_HEART_BEAT_REQ    MsgID = 1001 // 心跳检测请求
	MsgID_GATE_HEART_BEAT_RSP    MsgID = 1002 // 心跳检测应答
	MsgID_GATE_ANOTHER_LOGIN_NTF MsgID = 1004 // 顶号通知
	MsgID_GATE_KICK_OUT_NTF      MsgID = 1005 // 踢出通知
	MsgID_CMD_GATE_END           MsgID = 1099
	// ********************************************************************************
	MsgID_CMD_LOGIN_BEGIN        MsgID = 1100
	MsgID_CMD_LOGIN_REQ          MsgID = 1101 // 登录请求
	MsgID_CMD_LOGIN_RSP          MsgID = 1102 // 登录应答
	MsgID_CMD_LOGOUT_REQ         MsgID = 1103 // 登出请求
	MsgID_CMD_LOGOUT_RSP         MsgID = 1104 // 登出应答
	MsgID_CMD_DELETE_ACCOUNT_REQ MsgID = 1105 // 删除账号请求
	MsgID_CMD_DELETE_ACCOUNT_RSP MsgID = 1106 // 删除账号应答
	MsgID_CMD_LOGIN_END          MsgID = 1199
	// ********************************************************************************
	MsgID_CMD_HALL_BEGIN                       MsgID = 2000
	MsgID_CMD_GET_ROOM_INFO_REQ                MsgID = 2001 // 获取房间信息请求
	MsgID_CMD_GET_ROOM_INFO_RSP                MsgID = 2002 // 获取房间信息应答
	MsgID_CMD_ENTER_FISHERY_REQ                MsgID = 2003 // 进入钓场请求
	MsgID_CMD_ENTER_FISHERY_RSP                MsgID = 2004 // 进入钓场应答
	MsgID_CMD_GET_ITEM_INFO_REQ                MsgID = 2005 // 获取物品信息请求
	MsgID_CMD_GET_ITEM_INFO_RSP                MsgID = 2006 // 获取物品信息应答
	MsgID_CMD_GET_ITEM_INFO_BY_TYPE_REQ        MsgID = 2007 // 获取类型物品请求
	MsgID_CMD_GET_ITEM_INFO_BY_TYPE_RSP        MsgID = 2008 // 获取类型物品应答
	MsgID_CMD_GET_GOODS_BUY_INFO_REQ           MsgID = 2009 // 获取购买物品信息请求
	MsgID_CMD_GET_GOODS_BUY_INFO_RSP           MsgID = 2010 // 获取购买物品信息应答
	MsgID_CMD_STORE_BUY_REQ                    MsgID = 2011 // 商店购买请求
	MsgID_CMD_STORE_BUY_RSP                    MsgID = 2012 // 商店购买应答
	MsgID_CMD_UPDATE_ITEM_NTF                  MsgID = 2014 // 更新物品信息推送
	MsgID_CMD_GET_LAST_GAME_INFO_REQ           MsgID = 2015 // 获取上局游戏信息请求
	MsgID_CMD_GET_LAST_GAME_INFO_RSP           MsgID = 2016 // 获取上局游戏信息应答
	MsgID_CMD_GET_PLAYER_INFO_REQ              MsgID = 2017 // 获取玩家信息请求
	MsgID_CMD_GET_PLAYER_INFO_RSP              MsgID = 2018 // 获取玩家信息应答
	MsgID_CMD_USE_ITEM_REQ                     MsgID = 2019 // 使用物品请求
	MsgID_CMD_USE_ITEM_RSP                     MsgID = 2020 // 使用物品应答
	MsgID_CMD_EXP_LEVEL_CHANGE_NTF             MsgID = 2022 // 经验等级变化通知
	MsgID_CMD_FIRST_ENTER_HALL_REQ             MsgID = 2023 // 首次进入大厅请求
	MsgID_CMD_FIRST_ENTER_HALL_RSP             MsgID = 2024 // 首次进入大厅应答
	MsgID_CMD_STORE_MULTI_BUY_REQ              MsgID = 2025 // 商店批量购买请求
	MsgID_CMD_STORE_MULTI_BUY_RSP              MsgID = 2026 // 商店批量购买应答
	MsgID_CMD_GET_ROD_RIG_INFO_REQ             MsgID = 2027 // 获取钓组信息请求
	MsgID_CMD_GET_ROD_RIG_INFO_RSP             MsgID = 2028 // 获取钓组信息应答
	MsgID_CMD_UPDATE_ROD_RIG_INFO_REQ          MsgID = 2029 // 更新钓组信息请求
	MsgID_CMD_UPDATE_ROD_RIG_INFO_RSP          MsgID = 2030 // 更新钓组信息应答
	MsgID_CMD_DELETE_ROD_RIG_INFO_REQ          MsgID = 2031 // 删除钓组信息请求
	MsgID_CMD_DELETE_ROD_RIG_INFO_RSP          MsgID = 2032 // 删除钓组信息应答
	MsgID_CMD_MODIFY_PLAYER_NAME_REQ           MsgID = 2033 // 修改玩家名称请求
	MsgID_CMD_MODIFY_PLAYER_NAME_RSP           MsgID = 2034 // 修改玩家名称应答
	MsgID_CMD_MODIFY_PLAYER_AVATAR_REQ         MsgID = 2035 // 修改玩家头像请求
	MsgID_CMD_MODIFY_PLAYER_AVATAR_RSP         MsgID = 2036 // 修改玩家头像应答
	MsgID_CMD_MODIFY_PLAYER_FRAME_REQ          MsgID = 2037 // 修改玩家头像框请求
	MsgID_CMD_MODIFY_PLAYER_FRAME_RSP          MsgID = 2038 // 修改玩家头像框应答
	MsgID_CMD_PLAYER_INFO_UPDATE_NTF           MsgID = 2040 // 玩家信息更新
	MsgID_CMD_GET_STAT_LIST_REQ                MsgID = 2041 // 获取统计列表请求
	MsgID_CMD_GET_STAT_LIST_RSP                MsgID = 2042 // 获取统计列表应答
	MsgID_CMD_STAT_INFO_UPDATE_NTF             MsgID = 2044 // 统计信息更新通知
	MsgID_CMD_GET_TRIP_ROD_REQ                 MsgID = 2045 // 获取旅途钓组信息
	MsgID_CMD_GET_TRIP_ROD_RSP                 MsgID = 2046 // 获取旅途钓组信息
	MsgID_CMD_LOAD_TRIP_ROD_REQ                MsgID = 2047 // 加载钓组
	MsgID_CMD_LOAD_TRIP_ROD_RSP                MsgID = 2048 // 加载钓组
	MsgID_CMD_DEL_TRIP_ROD_REQ                 MsgID = 2049 // 卸下钓组
	MsgID_CMD_DEL_TRIP_ROD_RSP                 MsgID = 2050 // 卸下钓组
	MsgID_CMD_UPDATE_TRIP_ROD_REQ              MsgID = 2051 // 更新钓组
	MsgID_CMD_UPDATE_TRIP_ROD_RSP              MsgID = 2052 // 更新钓组
	MsgID_CMD_GET_TRIP_BAG_REQ                 MsgID = 2053 // 获取旅途背包信息
	MsgID_CMD_GET_TRIP_BAG_RSP                 MsgID = 2054 // 获取旅途背包信息
	MsgID_CMD_MODIFY_TRIP_BAG_REQ              MsgID = 2055 // 修改背包信息
	MsgID_CMD_MODIFY_TRIP_BAG_RSP              MsgID = 2056 // 修改背包信息
	MsgID_CMD_TRIP_BAG_QUICK_BUY_REQ           MsgID = 2057 // 旅途背包快捷购买
	MsgID_CMD_TRIP_BAG_QUICK_BUY_RSP           MsgID = 2058 // 旅途背包快捷购买
	MsgID_CMD_TRIP_BAG_USE_REQ                 MsgID = 2059 // 旅途背包使用使用
	MsgID_CMD_TRIP_BAG_USE_RSP                 MsgID = 2060 // 旅途背包使用使用
	MsgID_CMD_CHECK_FORBID_WORD_REQ            MsgID = 2061 // 检查屏蔽字请求
	MsgID_CMD_CHECK_FORBID_WORD_RSP            MsgID = 2062 // 检查屏蔽字应答
	MsgID_CMD_REAL_NAME_AUTH_REQ               MsgID = 2063 // 实名认证请求请求
	MsgID_CMD_REAL_NAME_AUTH_RSP               MsgID = 2064 // 实名认证请求应答
	MsgID_CMD_MODIFY_DURABILITY_REQ            MsgID = 2069 // 扣除杆组耐久
	MsgID_CMD_MODIFY_DURABILITY_RSP            MsgID = 2070 // 扣除杆组耐久
	MsgID_CMD_MAINTAIN_ROD_ITEM_REQ            MsgID = 2071 // 维修杆组耐久
	MsgID_CMD_MAINTAIN_ROD_ITEM_RSP            MsgID = 2072 // 维修杆组耐久
	MsgID_CMD_MAINTAIN_STORAGE_ITEM_REQ        MsgID = 2073 // 维修仓库道具
	MsgID_CMD_MAINTAIN_STORAGE_ITEM_RSP        MsgID = 2074 // 维修仓库道具
	MsgID_CMD_SAVE_TRIP_ROD_REQ                MsgID = 2075 // 保存鱼杆方案到仓库
	MsgID_CMD_SAVE_TRIP_ROD_RSP                MsgID = 2076 // 保存鱼杆方案到仓库
	MsgID_CMD_PUT_TRIP_ROD_REQ                 MsgID = 2077 // 从仓库放入鱼杆背包
	MsgID_CMD_PUT_TRIP_ROD_RSP                 MsgID = 2078 // 从仓库放入鱼杆背包
	MsgID_CMD_UNLOAD_TRIP_BAG_REQ              MsgID = 2079 // 卸下背包所有物品
	MsgID_CMD_UNLOAD_TRIP_BAG_RSP              MsgID = 2080 // 卸下背包所有物品
	MsgID_CMD_UPDATE_ROD_RIG_NTF               MsgID = 2082 // 更新杆组信息
	MsgID_CMD_SELL_ITEM_REQ                    MsgID = 2083 // 道具出售
	MsgID_CMD_SELL_ITEM_RSP                    MsgID = 2084 // 道具出售
	MsgID_CMD_BATCH_UPDATE_TRIP_ROD_REQ        MsgID = 2085 // 批量更新钓组
	MsgID_CMD_BATCH_UPDATE_TRIP_ROD_RSP        MsgID = 2086 // 批量更新钓组
	MsgID_CMD_ITEM_HEAP_REQ                    MsgID = 2087 // 鱼饵数据请求
	MsgID_CMD_ITEM_HEAP_RSP                    MsgID = 2088 // 鱼饵数据返回
	MsgID_CMD_ITEM_HEAP_UPDATE_NTF             MsgID = 2090 // 鱼饵更新通知
	MsgID_CMD_SPLIT_TRIP_ROD_REQ               MsgID = 2091 // 拆卸钓组
	MsgID_CMD_SPLIT_TRIP_ROD_RSP               MsgID = 2092 // 拆卸钓组
	MsgID_CMD_TEMP_CASH_BUY_REQ                MsgID = 2093 // 临时现金购买请求
	MsgID_CMD_TEMP_CASH_BUY_RSP                MsgID = 2094 // 临时现金购买应答
	MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ   MsgID = 2095 // 新手引导进度更新请求
	MsgID_CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP   MsgID = 2096 // 新手引导进度更新应答
	MsgID_CMD_HALL_CONTINUOUS_LOGIN_REQ        MsgID = 2097 // 登录登录请求
	MsgID_CMD_HALL_CONTINUOUS_LOGIN_RSP        MsgID = 2098 // 登录登录应答
	MsgID_CMD_HALL_CONTINUOUS_LOGIN_REWARD_REQ MsgID = 2099 // 登录登录奖励请求
	MsgID_CMD_HALL_CONTINUOUS_LOGIN_REWARD_RSP MsgID = 2100 // 登录登录奖励应答
	MsgID_CMD_SAVE_NEW_TRIP_ROD_REQ            MsgID = 2101 // 保存新杆组到仓库
	MsgID_CMD_SAVE_NEW_TRIP_ROD_RSP            MsgID = 2102 // 保存新杆组到仓库
	MsgID_CMD_CLEAR_RED_DOT_REQ                MsgID = 2103 // 清除红点
	MsgID_CMD_CLEAR_RED_DOT_RSP                MsgID = 2104 // 清除红点
	MsgID_CMD_GET_PLAYER_ALL_RED_DOT_REQ       MsgID = 2105 // 获取玩家所有红点
	MsgID_CMD_GET_PLAYER_ALL_RED_DOT_RSP       MsgID = 2106 // 获取玩家所有红点
	MsgID_CMD_GET_PLAYER_MODULE_RED_DOT_REQ    MsgID = 2107 // 获取玩家指定模块红点
	MsgID_CMD_GET_PLAYER_MODULE_RED_DOT_RSP    MsgID = 2108 // 获取玩家指定模块红点
	MsgID_CMD_PLAYER_RED_DOT_UPDATE_NTF        MsgID = 2110 // 玩家红点推送
	MsgID_CMD_HALL_MODIFY_PLAYER_INFO_REQ      MsgID = 2111 // 修改玩家信息请求
	MsgID_CMD_HALL_MODIFY_PLAYER_INFO_RSP      MsgID = 2112 // 修改玩家信息应答
	MsgID_CMD_CDKEY_EXCHANGE_REQ               MsgID = 2113 // CDKEY兑换请求
	MsgID_CMD_CDKEY_EXCHANGE_RSP               MsgID = 2114 // CDKEY兑换应答
	MsgID_CMD_HALL_GET_STATS_RULES_REQ         MsgID = 2115 // 获取统计规则请求
	MsgID_CMD_HALL_GET_STATS_RULES_RSP         MsgID = 2116 // 获取统计规则应答
	MsgID_CMD_HALL_GET_ITEM_CD_REQ             MsgID = 2117 // 获取物品冷却信息
	MsgID_CMD_HALL_GET_ITEM_CD_RSP             MsgID = 2118 // 获取物品冷却信息
	MsgID_CMD_HALL_POND_STORE_REQ              MsgID = 2119 // 局内商城请求
	MsgID_CMD_HALL_POND_STORE_RSP              MsgID = 2120 // 局内商城响应
	MsgID_CMD_HALL_POND_STORE_BUY_REQ          MsgID = 2121 // 局内商城购买请求
	MsgID_CMD_HALL_POND_STORE_BUY_RSP          MsgID = 2122 // 局内商城购买响应
	MsgID_CMD_HALL_END                         MsgID = 2999
	// ********************************************************************************
	MsgID_CMD_SPOT_BEGIN                   MsgID = 3000
	MsgID_CMD_GET_SPOT_SCENE_REQ           MsgID = 3001 // 钓点场景请求
	MsgID_CMD_GET_SPOT_SCENE_RSP           MsgID = 3002 // 钓点场景应答
	MsgID_CMD_ENTER_ROOM_BS_NTF            MsgID = 3004 // 进入房间广播
	MsgID_CMD_SYNC_SPOT_INFO_REQ           MsgID = 3005 // 同步钓点信息请求
	MsgID_CMD_SYNC_SPOT_INFO_BS_NTF        MsgID = 3006 // 同步钓点信息广播
	MsgID_CMD_THROW_ROD_REQ                MsgID = 3007 // 抛竿请求
	MsgID_CMD_THROW_ROD_RSP                MsgID = 3008 // 抛竿应答
	MsgID_CMD_FISH_HOOK_REQ                MsgID = 3009 // 中鱼请求
	MsgID_CMD_FISH_HOOK_RSP                MsgID = 3010 // 中鱼应答
	MsgID_CMD_CATCH_ROD_REQ                MsgID = 3011 // 收竿请求
	MsgID_CMD_CATCH_ROD_RSP                MsgID = 3012 // 收竿应答
	MsgID_CMD_EXIT_ROOM_REQ                MsgID = 3013 // 退出房间请求
	MsgID_CMD_EXIT_ROOM_RSP                MsgID = 3014 // 退出房间应答
	MsgID_CMD_EXIT_ROOM_BS_NTF             MsgID = 3016 // 退出房间广播
	MsgID_CMD_FISH_ENTRY_OPT_REQ           MsgID = 3017 // 鱼入护操作请求
	MsgID_CMD_FISH_ENTRY_OPT_RSP           MsgID = 3018 // 鱼入护操作应答
	MsgID_CMD_PLAYER_FISH_ENTRY_BS_NTF     MsgID = 3020 // 鱼入护广播
	MsgID_CMD_FISH_KEEPNET_OPT_REQ         MsgID = 3021 // 鱼护操作请求
	MsgID_CMD_FISH_KEEPNET_OPT_RSP         MsgID = 3022 // 鱼护操作应答
	MsgID_CMD_KEEPNET_FISH_INFO_REQ        MsgID = 3023 // 鱼护中鱼信息请求
	MsgID_CMD_KEEPNET_FISH_INFO_RSP        MsgID = 3024 // 鱼护中鱼信息应答
	MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_REQ MsgID = 3025 // 获取房间玩家信息请求
	MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_RSP MsgID = 3026 // 获取房间玩家信息应答
	MsgID_CMD_CHOOSE_SPOT_REQ              MsgID = 3027 // 选择钓点请求
	MsgID_CMD_CHOOSE_SPOT_RSP              MsgID = 3028 // 选择钓点应答
	MsgID_CMD_EXIT_ROOM_SETTLE_NTF         MsgID = 3029 // 退出结算通知
	MsgID_CMD_ENERGY_CHANGE_NTF            MsgID = 3030 // 体力变化通知
	MsgID_CMD_FISH_BATTLE_FISH_REQ         MsgID = 3031 // 搏鱼请求
	MsgID_CMD_FISH_BATTLE_FISH_RSP         MsgID = 3032 // 搏鱼应答
	MsgID_CMD_SWITCH_ROD_RIG_REQ           MsgID = 3033 // 切换鱼竿请求
	MsgID_CMD_SWITCH_ROD_RIG_RSP           MsgID = 3034 // 切换鱼竿响应
	MsgID_CMD_PLAYER_ENERGY_COST_REQ       MsgID = 3035 // 用户体力消耗
	MsgID_CMD_PLAYER_ENERGY_COST_RSP       MsgID = 3036 // 用户体力消耗
	MsgID_CMD_SPOT_FISHING_EVENT_REQ       MsgID = 3037 // 中鱼事件请求
	MsgID_CMD_SPOT_FISHING_EVENT_RSP       MsgID = 3038 // 中鱼事件应答
	MsgID_CMD_SPOT_FISHING_EVENT_BS_NTF    MsgID = 3039 // 中鱼事件广播
	MsgID_CMD_SPOT_HOOK_START_REQ          MsgID = 3040 // 开始中鱼
	MsgID_CMD_SPOT_HOOK_START_RSP          MsgID = 3041 // 开始中鱼
	MsgID_CMD_SPOT_KILL_LINE_REQ           MsgID = 3042 // 切线请求
	MsgID_CMD_SPOT_KILL_LINE_RSP           MsgID = 3043 // 切线应答
	MsgID_CMD_SPOT_END                     MsgID = 3099
	// ********************************************************************************
	MsgID_CMD_WORLD_BEGIN                  MsgID = 3200
	MsgID_CMD_WORLD_GET_WEATHER_REQ        MsgID = 3201 // 获取天气信息请求
	MsgID_CMD_WORLD_GET_WEATHER_RSP        MsgID = 3202 // 获取天气信息应答
	MsgID_CMD_WORLD_GET_GAMETIME_REQ       MsgID = 3203 // 获取游戏时间请求
	MsgID_CMD_WORLD_GET_GAMETIME_RSP       MsgID = 3204 // 获取游戏时间应答
	MsgID_CMD_WORLD_GET_WORLD_TIME_REQ     MsgID = 3205 // 获取服务器时间请求
	MsgID_CMD_WORLD_GET_WORLD_TIME_RSP     MsgID = 3206 // 获取服务器时间应答
	MsgID_CMD_WORLD_UPDATE_SERVER_TIME_NTF MsgID = 3207 // 同步更新服务器时间
	MsgID_CMD_WORLD_END                    MsgID = 3299 //********************************************************************************
	// ********************************************************************************
	MsgID_CMD_GM_BEGIN       MsgID = 3300
	MsgID_CMD_GM_OPERATE_REQ MsgID = 3301
	MsgID_CMD_GM_OPERATE_RSP MsgID = 3302
	MsgID_CMD_GM_END         MsgID = 3399
	// ********************************************************************************
	MsgID_CMD_PAY_BEGIN                      MsgID = 3400
	MsgID_CMD_PAY_CREATE_PURCHASE_ORDER_REQ  MsgID = 3401 // 创建订单
	MsgID_CMD_PAY_CREATE_PURCHASE_ORDER_RSP  MsgID = 3402
	MsgID_CMD_PAY_DELIVER_PURCHASE_ORDER_REQ MsgID = 3403 // 订单发货
	MsgID_CMD_PAY_DELIVER_PURCHASE_ORDER_RSP MsgID = 3404
	MsgID_CMD_PAY_CLOSE_PURCHASE_ORDER_REQ   MsgID = 3405 // 关闭订单
	MsgID_CMD_PAY_CLOSE_PURCHASE_ORDER_RSP   MsgID = 3406
	MsgID_CMD_PAY_END                        MsgID = 3499
	// ********************************************************************************
	MsgID_CMD_TASK_BEGIN               MsgID = 3500
	MsgID_CMD_TASK_GET_LIST_REQ        MsgID = 3501 // 全量更新
	MsgID_CMD_TASK_GET_LIST_RSP        MsgID = 3502
	MsgID_CMD_TASK_UPDATE_NTF          MsgID = 3504 // NTF
	MsgID_CMD_TASK_REWARD_REQ          MsgID = 3505 // 领取奖励
	MsgID_CMD_TASK_REWARD_RSP          MsgID = 3506
	MsgID_CMD_TASK_PROGRESS_REQ        MsgID = 3513 // 任务进度请求
	MsgID_CMD_TASK_PROGRESS_RSP        MsgID = 3514 // 任务进度返回
	MsgID_CMD_TASK_PROGRESS_NTF        MsgID = 3515 // 任务进度更新
	MsgID_CMD_TASK_REWARD_PROGRESS_REQ MsgID = 3517 // 任务进度奖励请求
	MsgID_CMD_TASK_REWARD_PROGRESS_RSP MsgID = 3518 // 任务进度奖励返回
	MsgID_CMD_PROGRESS_REQ             MsgID = 3519 // 进度请求
	MsgID_CMD_PROGRESS_RSP             MsgID = 3520 // 进度返回
	MsgID_CMD_PROGRESS_NTF             MsgID = 3521 // 进度更新
	MsgID_CMD_PROGRESS_REWARD_REQ      MsgID = 3523 // 进度奖励请求
	MsgID_CMD_PROGRESS_REWARD_RSP      MsgID = 3524 // 进度奖励返回
	MsgID_CMD_TASK_END                 MsgID = 3599
	// ********************************************************************************
	MsgID_CMD_MSG_BEGIN                   MsgID = 3600
	MsgID_CMD_MSG_GET_MAIL_LIST_REQ       MsgID = 3601 // 获取邮件列表
	MsgID_CMD_MSG_GET_MAIL_LIST_RSP       MsgID = 3602
	MsgID_CMD_MSG_READ_MAIL_REQ           MsgID = 3603 // 读邮件
	MsgID_CMD_MSG_READ_MAIL_RSP           MsgID = 3604
	MsgID_CMD_MSG_CLAIM_REWARD_ATTACH_REQ MsgID = 3605 // 领取附件
	MsgID_CMD_MSG_CLAIM_REWARD_ATTACH_RSP MsgID = 3606
	MsgID_CMD_MSG_NEW_MAIL_NTF            MsgID = 3608 // 邮件通知
	MsgID_CMD_MSG_BROADCAST               MsgID = 3610 // 广播
	MsgID_CMD_MSG_NEW_SYSTEM_MAIL_NTF     MsgID = 3612 // 系统邮件通知
	MsgID_CMD_MSG_END                     MsgID = 3699
	// ********************************************************************************
	MsgID_CMD_SYNC_BEGIN MsgID = 3700
	MsgID_CMD_SYNC_END   MsgID = 3799 //********************************************************************************
	// ********************************************************************************
	MsgID_CMD_RANK_BEGIN             MsgID = 3800
	MsgID_CMD_RANK_GET_RANK_LIST_REQ MsgID = 3801 // 获取排行榜信息
	MsgID_CMD_RANK_GET_RANK_LIST_RSP MsgID = 3802 // 获取排行榜信息
	MsgID_CMD_RANK_END               MsgID = 3899 //********************************************************************************
	// ********************************************************************************
	MsgID_CMD_TA_BEGIN           MsgID = 3900
	MsgID_CMD_TA_DATA_REPORT_REQ MsgID = 3901 // 数据上报
	MsgID_CMD_TA_DATA_REPORT_RSP MsgID = 3902 // 数据上报
	MsgID_CMD_TA_END             MsgID = 3999 //********************************************************************************
	// ********************************************************************************
	MsgID_CMD_ACTIVITY_BEGIN            MsgID = 4000
	MsgID_CMD_GET_ACTIVITY_PROGRESS_REQ MsgID = 4001 // 获取活动进度请求
	MsgID_CMD_GET_ACTIVITY_PROGRESS_RSP MsgID = 4002 // 获取活动进度响应
	MsgID_CMD_CLAIM_ACTIVITY_REWARD_REQ MsgID = 4003 // 领取活动奖励请求
	MsgID_CMD_CLAIM_ACTIVITY_REWARD_RSP MsgID = 4004 // 领取活动奖励响应
	MsgID_CMD_ACTIVITY_END              MsgID = 4099 //********************************************************************************
	MsgID_CMD_END                       MsgID = 9999
)

// Enum value maps for MsgID.
var (
	MsgID_name = map[int32]string{
		0:    "CMD_BEGIN",
		1000: "CMD_GATE_BEGIN",
		1001: "GATE_HEART_BEAT_REQ",
		1002: "GATE_HEART_BEAT_RSP",
		1004: "GATE_ANOTHER_LOGIN_NTF",
		1005: "GATE_KICK_OUT_NTF",
		1099: "CMD_GATE_END",
		1100: "CMD_LOGIN_BEGIN",
		1101: "CMD_LOGIN_REQ",
		1102: "CMD_LOGIN_RSP",
		1103: "CMD_LOGOUT_REQ",
		1104: "CMD_LOGOUT_RSP",
		1105: "CMD_DELETE_ACCOUNT_REQ",
		1106: "CMD_DELETE_ACCOUNT_RSP",
		1199: "CMD_LOGIN_END",
		2000: "CMD_HALL_BEGIN",
		2001: "CMD_GET_ROOM_INFO_REQ",
		2002: "CMD_GET_ROOM_INFO_RSP",
		2003: "CMD_ENTER_FISHERY_REQ",
		2004: "CMD_ENTER_FISHERY_RSP",
		2005: "CMD_GET_ITEM_INFO_REQ",
		2006: "CMD_GET_ITEM_INFO_RSP",
		2007: "CMD_GET_ITEM_INFO_BY_TYPE_REQ",
		2008: "CMD_GET_ITEM_INFO_BY_TYPE_RSP",
		2009: "CMD_GET_GOODS_BUY_INFO_REQ",
		2010: "CMD_GET_GOODS_BUY_INFO_RSP",
		2011: "CMD_STORE_BUY_REQ",
		2012: "CMD_STORE_BUY_RSP",
		2014: "CMD_UPDATE_ITEM_NTF",
		2015: "CMD_GET_LAST_GAME_INFO_REQ",
		2016: "CMD_GET_LAST_GAME_INFO_RSP",
		2017: "CMD_GET_PLAYER_INFO_REQ",
		2018: "CMD_GET_PLAYER_INFO_RSP",
		2019: "CMD_USE_ITEM_REQ",
		2020: "CMD_USE_ITEM_RSP",
		2022: "CMD_EXP_LEVEL_CHANGE_NTF",
		2023: "CMD_FIRST_ENTER_HALL_REQ",
		2024: "CMD_FIRST_ENTER_HALL_RSP",
		2025: "CMD_STORE_MULTI_BUY_REQ",
		2026: "CMD_STORE_MULTI_BUY_RSP",
		2027: "CMD_GET_ROD_RIG_INFO_REQ",
		2028: "CMD_GET_ROD_RIG_INFO_RSP",
		2029: "CMD_UPDATE_ROD_RIG_INFO_REQ",
		2030: "CMD_UPDATE_ROD_RIG_INFO_RSP",
		2031: "CMD_DELETE_ROD_RIG_INFO_REQ",
		2032: "CMD_DELETE_ROD_RIG_INFO_RSP",
		2033: "CMD_MODIFY_PLAYER_NAME_REQ",
		2034: "CMD_MODIFY_PLAYER_NAME_RSP",
		2035: "CMD_MODIFY_PLAYER_AVATAR_REQ",
		2036: "CMD_MODIFY_PLAYER_AVATAR_RSP",
		2037: "CMD_MODIFY_PLAYER_FRAME_REQ",
		2038: "CMD_MODIFY_PLAYER_FRAME_RSP",
		2040: "CMD_PLAYER_INFO_UPDATE_NTF",
		2041: "CMD_GET_STAT_LIST_REQ",
		2042: "CMD_GET_STAT_LIST_RSP",
		2044: "CMD_STAT_INFO_UPDATE_NTF",
		2045: "CMD_GET_TRIP_ROD_REQ",
		2046: "CMD_GET_TRIP_ROD_RSP",
		2047: "CMD_LOAD_TRIP_ROD_REQ",
		2048: "CMD_LOAD_TRIP_ROD_RSP",
		2049: "CMD_DEL_TRIP_ROD_REQ",
		2050: "CMD_DEL_TRIP_ROD_RSP",
		2051: "CMD_UPDATE_TRIP_ROD_REQ",
		2052: "CMD_UPDATE_TRIP_ROD_RSP",
		2053: "CMD_GET_TRIP_BAG_REQ",
		2054: "CMD_GET_TRIP_BAG_RSP",
		2055: "CMD_MODIFY_TRIP_BAG_REQ",
		2056: "CMD_MODIFY_TRIP_BAG_RSP",
		2057: "CMD_TRIP_BAG_QUICK_BUY_REQ",
		2058: "CMD_TRIP_BAG_QUICK_BUY_RSP",
		2059: "CMD_TRIP_BAG_USE_REQ",
		2060: "CMD_TRIP_BAG_USE_RSP",
		2061: "CMD_CHECK_FORBID_WORD_REQ",
		2062: "CMD_CHECK_FORBID_WORD_RSP",
		2063: "CMD_REAL_NAME_AUTH_REQ",
		2064: "CMD_REAL_NAME_AUTH_RSP",
		2069: "CMD_MODIFY_DURABILITY_REQ",
		2070: "CMD_MODIFY_DURABILITY_RSP",
		2071: "CMD_MAINTAIN_ROD_ITEM_REQ",
		2072: "CMD_MAINTAIN_ROD_ITEM_RSP",
		2073: "CMD_MAINTAIN_STORAGE_ITEM_REQ",
		2074: "CMD_MAINTAIN_STORAGE_ITEM_RSP",
		2075: "CMD_SAVE_TRIP_ROD_REQ",
		2076: "CMD_SAVE_TRIP_ROD_RSP",
		2077: "CMD_PUT_TRIP_ROD_REQ",
		2078: "CMD_PUT_TRIP_ROD_RSP",
		2079: "CMD_UNLOAD_TRIP_BAG_REQ",
		2080: "CMD_UNLOAD_TRIP_BAG_RSP",
		2082: "CMD_UPDATE_ROD_RIG_NTF",
		2083: "CMD_SELL_ITEM_REQ",
		2084: "CMD_SELL_ITEM_RSP",
		2085: "CMD_BATCH_UPDATE_TRIP_ROD_REQ",
		2086: "CMD_BATCH_UPDATE_TRIP_ROD_RSP",
		2087: "CMD_ITEM_HEAP_REQ",
		2088: "CMD_ITEM_HEAP_RSP",
		2090: "CMD_ITEM_HEAP_UPDATE_NTF",
		2091: "CMD_SPLIT_TRIP_ROD_REQ",
		2092: "CMD_SPLIT_TRIP_ROD_RSP",
		2093: "CMD_TEMP_CASH_BUY_REQ",
		2094: "CMD_TEMP_CASH_BUY_RSP",
		2095: "CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ",
		2096: "CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP",
		2097: "CMD_HALL_CONTINUOUS_LOGIN_REQ",
		2098: "CMD_HALL_CONTINUOUS_LOGIN_RSP",
		2099: "CMD_HALL_CONTINUOUS_LOGIN_REWARD_REQ",
		2100: "CMD_HALL_CONTINUOUS_LOGIN_REWARD_RSP",
		2101: "CMD_SAVE_NEW_TRIP_ROD_REQ",
		2102: "CMD_SAVE_NEW_TRIP_ROD_RSP",
		2103: "CMD_CLEAR_RED_DOT_REQ",
		2104: "CMD_CLEAR_RED_DOT_RSP",
		2105: "CMD_GET_PLAYER_ALL_RED_DOT_REQ",
		2106: "CMD_GET_PLAYER_ALL_RED_DOT_RSP",
		2107: "CMD_GET_PLAYER_MODULE_RED_DOT_REQ",
		2108: "CMD_GET_PLAYER_MODULE_RED_DOT_RSP",
		2110: "CMD_PLAYER_RED_DOT_UPDATE_NTF",
		2111: "CMD_HALL_MODIFY_PLAYER_INFO_REQ",
		2112: "CMD_HALL_MODIFY_PLAYER_INFO_RSP",
		2113: "CMD_CDKEY_EXCHANGE_REQ",
		2114: "CMD_CDKEY_EXCHANGE_RSP",
		2115: "CMD_HALL_GET_STATS_RULES_REQ",
		2116: "CMD_HALL_GET_STATS_RULES_RSP",
		2117: "CMD_HALL_GET_ITEM_CD_REQ",
		2118: "CMD_HALL_GET_ITEM_CD_RSP",
		2119: "CMD_HALL_POND_STORE_REQ",
		2120: "CMD_HALL_POND_STORE_RSP",
		2121: "CMD_HALL_POND_STORE_BUY_REQ",
		2122: "CMD_HALL_POND_STORE_BUY_RSP",
		2999: "CMD_HALL_END",
		3000: "CMD_SPOT_BEGIN",
		3001: "CMD_GET_SPOT_SCENE_REQ",
		3002: "CMD_GET_SPOT_SCENE_RSP",
		3004: "CMD_ENTER_ROOM_BS_NTF",
		3005: "CMD_SYNC_SPOT_INFO_REQ",
		3006: "CMD_SYNC_SPOT_INFO_BS_NTF",
		3007: "CMD_THROW_ROD_REQ",
		3008: "CMD_THROW_ROD_RSP",
		3009: "CMD_FISH_HOOK_REQ",
		3010: "CMD_FISH_HOOK_RSP",
		3011: "CMD_CATCH_ROD_REQ",
		3012: "CMD_CATCH_ROD_RSP",
		3013: "CMD_EXIT_ROOM_REQ",
		3014: "CMD_EXIT_ROOM_RSP",
		3016: "CMD_EXIT_ROOM_BS_NTF",
		3017: "CMD_FISH_ENTRY_OPT_REQ",
		3018: "CMD_FISH_ENTRY_OPT_RSP",
		3020: "CMD_PLAYER_FISH_ENTRY_BS_NTF",
		3021: "CMD_FISH_KEEPNET_OPT_REQ",
		3022: "CMD_FISH_KEEPNET_OPT_RSP",
		3023: "CMD_KEEPNET_FISH_INFO_REQ",
		3024: "CMD_KEEPNET_FISH_INFO_RSP",
		3025: "CMD_GET_ROOM_ALL_PLAYER_INFO_REQ",
		3026: "CMD_GET_ROOM_ALL_PLAYER_INFO_RSP",
		3027: "CMD_CHOOSE_SPOT_REQ",
		3028: "CMD_CHOOSE_SPOT_RSP",
		3029: "CMD_EXIT_ROOM_SETTLE_NTF",
		3030: "CMD_ENERGY_CHANGE_NTF",
		3031: "CMD_FISH_BATTLE_FISH_REQ",
		3032: "CMD_FISH_BATTLE_FISH_RSP",
		3033: "CMD_SWITCH_ROD_RIG_REQ",
		3034: "CMD_SWITCH_ROD_RIG_RSP",
		3035: "CMD_PLAYER_ENERGY_COST_REQ",
		3036: "CMD_PLAYER_ENERGY_COST_RSP",
		3037: "CMD_SPOT_FISHING_EVENT_REQ",
		3038: "CMD_SPOT_FISHING_EVENT_RSP",
		3039: "CMD_SPOT_FISHING_EVENT_BS_NTF",
		3040: "CMD_SPOT_HOOK_START_REQ",
		3041: "CMD_SPOT_HOOK_START_RSP",
		3042: "CMD_SPOT_KILL_LINE_REQ",
		3043: "CMD_SPOT_KILL_LINE_RSP",
		3099: "CMD_SPOT_END",
		3200: "CMD_WORLD_BEGIN",
		3201: "CMD_WORLD_GET_WEATHER_REQ",
		3202: "CMD_WORLD_GET_WEATHER_RSP",
		3203: "CMD_WORLD_GET_GAMETIME_REQ",
		3204: "CMD_WORLD_GET_GAMETIME_RSP",
		3205: "CMD_WORLD_GET_WORLD_TIME_REQ",
		3206: "CMD_WORLD_GET_WORLD_TIME_RSP",
		3207: "CMD_WORLD_UPDATE_SERVER_TIME_NTF",
		3299: "CMD_WORLD_END",
		3300: "CMD_GM_BEGIN",
		3301: "CMD_GM_OPERATE_REQ",
		3302: "CMD_GM_OPERATE_RSP",
		3399: "CMD_GM_END",
		3400: "CMD_PAY_BEGIN",
		3401: "CMD_PAY_CREATE_PURCHASE_ORDER_REQ",
		3402: "CMD_PAY_CREATE_PURCHASE_ORDER_RSP",
		3403: "CMD_PAY_DELIVER_PURCHASE_ORDER_REQ",
		3404: "CMD_PAY_DELIVER_PURCHASE_ORDER_RSP",
		3405: "CMD_PAY_CLOSE_PURCHASE_ORDER_REQ",
		3406: "CMD_PAY_CLOSE_PURCHASE_ORDER_RSP",
		3499: "CMD_PAY_END",
		3500: "CMD_TASK_BEGIN",
		3501: "CMD_TASK_GET_LIST_REQ",
		3502: "CMD_TASK_GET_LIST_RSP",
		3504: "CMD_TASK_UPDATE_NTF",
		3505: "CMD_TASK_REWARD_REQ",
		3506: "CMD_TASK_REWARD_RSP",
		3513: "CMD_TASK_PROGRESS_REQ",
		3514: "CMD_TASK_PROGRESS_RSP",
		3515: "CMD_TASK_PROGRESS_NTF",
		3517: "CMD_TASK_REWARD_PROGRESS_REQ",
		3518: "CMD_TASK_REWARD_PROGRESS_RSP",
		3519: "CMD_PROGRESS_REQ",
		3520: "CMD_PROGRESS_RSP",
		3521: "CMD_PROGRESS_NTF",
		3523: "CMD_PROGRESS_REWARD_REQ",
		3524: "CMD_PROGRESS_REWARD_RSP",
		3599: "CMD_TASK_END",
		3600: "CMD_MSG_BEGIN",
		3601: "CMD_MSG_GET_MAIL_LIST_REQ",
		3602: "CMD_MSG_GET_MAIL_LIST_RSP",
		3603: "CMD_MSG_READ_MAIL_REQ",
		3604: "CMD_MSG_READ_MAIL_RSP",
		3605: "CMD_MSG_CLAIM_REWARD_ATTACH_REQ",
		3606: "CMD_MSG_CLAIM_REWARD_ATTACH_RSP",
		3608: "CMD_MSG_NEW_MAIL_NTF",
		3610: "CMD_MSG_BROADCAST",
		3612: "CMD_MSG_NEW_SYSTEM_MAIL_NTF",
		3699: "CMD_MSG_END",
		3700: "CMD_SYNC_BEGIN",
		3799: "CMD_SYNC_END",
		3800: "CMD_RANK_BEGIN",
		3801: "CMD_RANK_GET_RANK_LIST_REQ",
		3802: "CMD_RANK_GET_RANK_LIST_RSP",
		3899: "CMD_RANK_END",
		3900: "CMD_TA_BEGIN",
		3901: "CMD_TA_DATA_REPORT_REQ",
		3902: "CMD_TA_DATA_REPORT_RSP",
		3999: "CMD_TA_END",
		4000: "CMD_ACTIVITY_BEGIN",
		4001: "CMD_GET_ACTIVITY_PROGRESS_REQ",
		4002: "CMD_GET_ACTIVITY_PROGRESS_RSP",
		4003: "CMD_CLAIM_ACTIVITY_REWARD_REQ",
		4004: "CMD_CLAIM_ACTIVITY_REWARD_RSP",
		4099: "CMD_ACTIVITY_END",
		9999: "CMD_END",
	}
	MsgID_value = map[string]int32{
		"CMD_BEGIN":                            0,
		"CMD_GATE_BEGIN":                       1000,
		"GATE_HEART_BEAT_REQ":                  1001,
		"GATE_HEART_BEAT_RSP":                  1002,
		"GATE_ANOTHER_LOGIN_NTF":               1004,
		"GATE_KICK_OUT_NTF":                    1005,
		"CMD_GATE_END":                         1099,
		"CMD_LOGIN_BEGIN":                      1100,
		"CMD_LOGIN_REQ":                        1101,
		"CMD_LOGIN_RSP":                        1102,
		"CMD_LOGOUT_REQ":                       1103,
		"CMD_LOGOUT_RSP":                       1104,
		"CMD_DELETE_ACCOUNT_REQ":               1105,
		"CMD_DELETE_ACCOUNT_RSP":               1106,
		"CMD_LOGIN_END":                        1199,
		"CMD_HALL_BEGIN":                       2000,
		"CMD_GET_ROOM_INFO_REQ":                2001,
		"CMD_GET_ROOM_INFO_RSP":                2002,
		"CMD_ENTER_FISHERY_REQ":                2003,
		"CMD_ENTER_FISHERY_RSP":                2004,
		"CMD_GET_ITEM_INFO_REQ":                2005,
		"CMD_GET_ITEM_INFO_RSP":                2006,
		"CMD_GET_ITEM_INFO_BY_TYPE_REQ":        2007,
		"CMD_GET_ITEM_INFO_BY_TYPE_RSP":        2008,
		"CMD_GET_GOODS_BUY_INFO_REQ":           2009,
		"CMD_GET_GOODS_BUY_INFO_RSP":           2010,
		"CMD_STORE_BUY_REQ":                    2011,
		"CMD_STORE_BUY_RSP":                    2012,
		"CMD_UPDATE_ITEM_NTF":                  2014,
		"CMD_GET_LAST_GAME_INFO_REQ":           2015,
		"CMD_GET_LAST_GAME_INFO_RSP":           2016,
		"CMD_GET_PLAYER_INFO_REQ":              2017,
		"CMD_GET_PLAYER_INFO_RSP":              2018,
		"CMD_USE_ITEM_REQ":                     2019,
		"CMD_USE_ITEM_RSP":                     2020,
		"CMD_EXP_LEVEL_CHANGE_NTF":             2022,
		"CMD_FIRST_ENTER_HALL_REQ":             2023,
		"CMD_FIRST_ENTER_HALL_RSP":             2024,
		"CMD_STORE_MULTI_BUY_REQ":              2025,
		"CMD_STORE_MULTI_BUY_RSP":              2026,
		"CMD_GET_ROD_RIG_INFO_REQ":             2027,
		"CMD_GET_ROD_RIG_INFO_RSP":             2028,
		"CMD_UPDATE_ROD_RIG_INFO_REQ":          2029,
		"CMD_UPDATE_ROD_RIG_INFO_RSP":          2030,
		"CMD_DELETE_ROD_RIG_INFO_REQ":          2031,
		"CMD_DELETE_ROD_RIG_INFO_RSP":          2032,
		"CMD_MODIFY_PLAYER_NAME_REQ":           2033,
		"CMD_MODIFY_PLAYER_NAME_RSP":           2034,
		"CMD_MODIFY_PLAYER_AVATAR_REQ":         2035,
		"CMD_MODIFY_PLAYER_AVATAR_RSP":         2036,
		"CMD_MODIFY_PLAYER_FRAME_REQ":          2037,
		"CMD_MODIFY_PLAYER_FRAME_RSP":          2038,
		"CMD_PLAYER_INFO_UPDATE_NTF":           2040,
		"CMD_GET_STAT_LIST_REQ":                2041,
		"CMD_GET_STAT_LIST_RSP":                2042,
		"CMD_STAT_INFO_UPDATE_NTF":             2044,
		"CMD_GET_TRIP_ROD_REQ":                 2045,
		"CMD_GET_TRIP_ROD_RSP":                 2046,
		"CMD_LOAD_TRIP_ROD_REQ":                2047,
		"CMD_LOAD_TRIP_ROD_RSP":                2048,
		"CMD_DEL_TRIP_ROD_REQ":                 2049,
		"CMD_DEL_TRIP_ROD_RSP":                 2050,
		"CMD_UPDATE_TRIP_ROD_REQ":              2051,
		"CMD_UPDATE_TRIP_ROD_RSP":              2052,
		"CMD_GET_TRIP_BAG_REQ":                 2053,
		"CMD_GET_TRIP_BAG_RSP":                 2054,
		"CMD_MODIFY_TRIP_BAG_REQ":              2055,
		"CMD_MODIFY_TRIP_BAG_RSP":              2056,
		"CMD_TRIP_BAG_QUICK_BUY_REQ":           2057,
		"CMD_TRIP_BAG_QUICK_BUY_RSP":           2058,
		"CMD_TRIP_BAG_USE_REQ":                 2059,
		"CMD_TRIP_BAG_USE_RSP":                 2060,
		"CMD_CHECK_FORBID_WORD_REQ":            2061,
		"CMD_CHECK_FORBID_WORD_RSP":            2062,
		"CMD_REAL_NAME_AUTH_REQ":               2063,
		"CMD_REAL_NAME_AUTH_RSP":               2064,
		"CMD_MODIFY_DURABILITY_REQ":            2069,
		"CMD_MODIFY_DURABILITY_RSP":            2070,
		"CMD_MAINTAIN_ROD_ITEM_REQ":            2071,
		"CMD_MAINTAIN_ROD_ITEM_RSP":            2072,
		"CMD_MAINTAIN_STORAGE_ITEM_REQ":        2073,
		"CMD_MAINTAIN_STORAGE_ITEM_RSP":        2074,
		"CMD_SAVE_TRIP_ROD_REQ":                2075,
		"CMD_SAVE_TRIP_ROD_RSP":                2076,
		"CMD_PUT_TRIP_ROD_REQ":                 2077,
		"CMD_PUT_TRIP_ROD_RSP":                 2078,
		"CMD_UNLOAD_TRIP_BAG_REQ":              2079,
		"CMD_UNLOAD_TRIP_BAG_RSP":              2080,
		"CMD_UPDATE_ROD_RIG_NTF":               2082,
		"CMD_SELL_ITEM_REQ":                    2083,
		"CMD_SELL_ITEM_RSP":                    2084,
		"CMD_BATCH_UPDATE_TRIP_ROD_REQ":        2085,
		"CMD_BATCH_UPDATE_TRIP_ROD_RSP":        2086,
		"CMD_ITEM_HEAP_REQ":                    2087,
		"CMD_ITEM_HEAP_RSP":                    2088,
		"CMD_ITEM_HEAP_UPDATE_NTF":             2090,
		"CMD_SPLIT_TRIP_ROD_REQ":               2091,
		"CMD_SPLIT_TRIP_ROD_RSP":               2092,
		"CMD_TEMP_CASH_BUY_REQ":                2093,
		"CMD_TEMP_CASH_BUY_RSP":                2094,
		"CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ":   2095,
		"CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP":   2096,
		"CMD_HALL_CONTINUOUS_LOGIN_REQ":        2097,
		"CMD_HALL_CONTINUOUS_LOGIN_RSP":        2098,
		"CMD_HALL_CONTINUOUS_LOGIN_REWARD_REQ": 2099,
		"CMD_HALL_CONTINUOUS_LOGIN_REWARD_RSP": 2100,
		"CMD_SAVE_NEW_TRIP_ROD_REQ":            2101,
		"CMD_SAVE_NEW_TRIP_ROD_RSP":            2102,
		"CMD_CLEAR_RED_DOT_REQ":                2103,
		"CMD_CLEAR_RED_DOT_RSP":                2104,
		"CMD_GET_PLAYER_ALL_RED_DOT_REQ":       2105,
		"CMD_GET_PLAYER_ALL_RED_DOT_RSP":       2106,
		"CMD_GET_PLAYER_MODULE_RED_DOT_REQ":    2107,
		"CMD_GET_PLAYER_MODULE_RED_DOT_RSP":    2108,
		"CMD_PLAYER_RED_DOT_UPDATE_NTF":        2110,
		"CMD_HALL_MODIFY_PLAYER_INFO_REQ":      2111,
		"CMD_HALL_MODIFY_PLAYER_INFO_RSP":      2112,
		"CMD_CDKEY_EXCHANGE_REQ":               2113,
		"CMD_CDKEY_EXCHANGE_RSP":               2114,
		"CMD_HALL_GET_STATS_RULES_REQ":         2115,
		"CMD_HALL_GET_STATS_RULES_RSP":         2116,
		"CMD_HALL_GET_ITEM_CD_REQ":             2117,
		"CMD_HALL_GET_ITEM_CD_RSP":             2118,
		"CMD_HALL_POND_STORE_REQ":              2119,
		"CMD_HALL_POND_STORE_RSP":              2120,
		"CMD_HALL_POND_STORE_BUY_REQ":          2121,
		"CMD_HALL_POND_STORE_BUY_RSP":          2122,
		"CMD_HALL_END":                         2999,
		"CMD_SPOT_BEGIN":                       3000,
		"CMD_GET_SPOT_SCENE_REQ":               3001,
		"CMD_GET_SPOT_SCENE_RSP":               3002,
		"CMD_ENTER_ROOM_BS_NTF":                3004,
		"CMD_SYNC_SPOT_INFO_REQ":               3005,
		"CMD_SYNC_SPOT_INFO_BS_NTF":            3006,
		"CMD_THROW_ROD_REQ":                    3007,
		"CMD_THROW_ROD_RSP":                    3008,
		"CMD_FISH_HOOK_REQ":                    3009,
		"CMD_FISH_HOOK_RSP":                    3010,
		"CMD_CATCH_ROD_REQ":                    3011,
		"CMD_CATCH_ROD_RSP":                    3012,
		"CMD_EXIT_ROOM_REQ":                    3013,
		"CMD_EXIT_ROOM_RSP":                    3014,
		"CMD_EXIT_ROOM_BS_NTF":                 3016,
		"CMD_FISH_ENTRY_OPT_REQ":               3017,
		"CMD_FISH_ENTRY_OPT_RSP":               3018,
		"CMD_PLAYER_FISH_ENTRY_BS_NTF":         3020,
		"CMD_FISH_KEEPNET_OPT_REQ":             3021,
		"CMD_FISH_KEEPNET_OPT_RSP":             3022,
		"CMD_KEEPNET_FISH_INFO_REQ":            3023,
		"CMD_KEEPNET_FISH_INFO_RSP":            3024,
		"CMD_GET_ROOM_ALL_PLAYER_INFO_REQ":     3025,
		"CMD_GET_ROOM_ALL_PLAYER_INFO_RSP":     3026,
		"CMD_CHOOSE_SPOT_REQ":                  3027,
		"CMD_CHOOSE_SPOT_RSP":                  3028,
		"CMD_EXIT_ROOM_SETTLE_NTF":             3029,
		"CMD_ENERGY_CHANGE_NTF":                3030,
		"CMD_FISH_BATTLE_FISH_REQ":             3031,
		"CMD_FISH_BATTLE_FISH_RSP":             3032,
		"CMD_SWITCH_ROD_RIG_REQ":               3033,
		"CMD_SWITCH_ROD_RIG_RSP":               3034,
		"CMD_PLAYER_ENERGY_COST_REQ":           3035,
		"CMD_PLAYER_ENERGY_COST_RSP":           3036,
		"CMD_SPOT_FISHING_EVENT_REQ":           3037,
		"CMD_SPOT_FISHING_EVENT_RSP":           3038,
		"CMD_SPOT_FISHING_EVENT_BS_NTF":        3039,
		"CMD_SPOT_HOOK_START_REQ":              3040,
		"CMD_SPOT_HOOK_START_RSP":              3041,
		"CMD_SPOT_KILL_LINE_REQ":               3042,
		"CMD_SPOT_KILL_LINE_RSP":               3043,
		"CMD_SPOT_END":                         3099,
		"CMD_WORLD_BEGIN":                      3200,
		"CMD_WORLD_GET_WEATHER_REQ":            3201,
		"CMD_WORLD_GET_WEATHER_RSP":            3202,
		"CMD_WORLD_GET_GAMETIME_REQ":           3203,
		"CMD_WORLD_GET_GAMETIME_RSP":           3204,
		"CMD_WORLD_GET_WORLD_TIME_REQ":         3205,
		"CMD_WORLD_GET_WORLD_TIME_RSP":         3206,
		"CMD_WORLD_UPDATE_SERVER_TIME_NTF":     3207,
		"CMD_WORLD_END":                        3299,
		"CMD_GM_BEGIN":                         3300,
		"CMD_GM_OPERATE_REQ":                   3301,
		"CMD_GM_OPERATE_RSP":                   3302,
		"CMD_GM_END":                           3399,
		"CMD_PAY_BEGIN":                        3400,
		"CMD_PAY_CREATE_PURCHASE_ORDER_REQ":    3401,
		"CMD_PAY_CREATE_PURCHASE_ORDER_RSP":    3402,
		"CMD_PAY_DELIVER_PURCHASE_ORDER_REQ":   3403,
		"CMD_PAY_DELIVER_PURCHASE_ORDER_RSP":   3404,
		"CMD_PAY_CLOSE_PURCHASE_ORDER_REQ":     3405,
		"CMD_PAY_CLOSE_PURCHASE_ORDER_RSP":     3406,
		"CMD_PAY_END":                          3499,
		"CMD_TASK_BEGIN":                       3500,
		"CMD_TASK_GET_LIST_REQ":                3501,
		"CMD_TASK_GET_LIST_RSP":                3502,
		"CMD_TASK_UPDATE_NTF":                  3504,
		"CMD_TASK_REWARD_REQ":                  3505,
		"CMD_TASK_REWARD_RSP":                  3506,
		"CMD_TASK_PROGRESS_REQ":                3513,
		"CMD_TASK_PROGRESS_RSP":                3514,
		"CMD_TASK_PROGRESS_NTF":                3515,
		"CMD_TASK_REWARD_PROGRESS_REQ":         3517,
		"CMD_TASK_REWARD_PROGRESS_RSP":         3518,
		"CMD_PROGRESS_REQ":                     3519,
		"CMD_PROGRESS_RSP":                     3520,
		"CMD_PROGRESS_NTF":                     3521,
		"CMD_PROGRESS_REWARD_REQ":              3523,
		"CMD_PROGRESS_REWARD_RSP":              3524,
		"CMD_TASK_END":                         3599,
		"CMD_MSG_BEGIN":                        3600,
		"CMD_MSG_GET_MAIL_LIST_REQ":            3601,
		"CMD_MSG_GET_MAIL_LIST_RSP":            3602,
		"CMD_MSG_READ_MAIL_REQ":                3603,
		"CMD_MSG_READ_MAIL_RSP":                3604,
		"CMD_MSG_CLAIM_REWARD_ATTACH_REQ":      3605,
		"CMD_MSG_CLAIM_REWARD_ATTACH_RSP":      3606,
		"CMD_MSG_NEW_MAIL_NTF":                 3608,
		"CMD_MSG_BROADCAST":                    3610,
		"CMD_MSG_NEW_SYSTEM_MAIL_NTF":          3612,
		"CMD_MSG_END":                          3699,
		"CMD_SYNC_BEGIN":                       3700,
		"CMD_SYNC_END":                         3799,
		"CMD_RANK_BEGIN":                       3800,
		"CMD_RANK_GET_RANK_LIST_REQ":           3801,
		"CMD_RANK_GET_RANK_LIST_RSP":           3802,
		"CMD_RANK_END":                         3899,
		"CMD_TA_BEGIN":                         3900,
		"CMD_TA_DATA_REPORT_REQ":               3901,
		"CMD_TA_DATA_REPORT_RSP":               3902,
		"CMD_TA_END":                           3999,
		"CMD_ACTIVITY_BEGIN":                   4000,
		"CMD_GET_ACTIVITY_PROGRESS_REQ":        4001,
		"CMD_GET_ACTIVITY_PROGRESS_RSP":        4002,
		"CMD_CLAIM_ACTIVITY_REWARD_REQ":        4003,
		"CMD_CLAIM_ACTIVITY_REWARD_RSP":        4004,
		"CMD_ACTIVITY_END":                     4099,
		"CMD_END":                              9999,
	}
)

func (x MsgID) Enum() *MsgID {
	p := new(MsgID)
	*p = x
	return p
}

func (x MsgID) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgID) Descriptor() protoreflect.EnumDescriptor {
	return file_msgid_proto_enumTypes[0].Descriptor()
}

func (MsgID) Type() protoreflect.EnumType {
	return &file_msgid_proto_enumTypes[0]
}

func (x MsgID) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgID.Descriptor instead.
func (MsgID) EnumDescriptor() ([]byte, []int) {
	return file_msgid_proto_rawDescGZIP(), []int{0}
}

var File_msgid_proto protoreflect.FileDescriptor

var file_msgid_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6d, 0x73, 0x67, 0x69, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2a, 0x9b, 0x36, 0x0a, 0x05, 0x4d, 0x73, 0x67, 0x49, 0x44, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x4d, 0x44, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x13,
	0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e,
	0x10, 0xe8, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x52,
	0x54, 0x5f, 0x42, 0x45, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe9, 0x07, 0x12, 0x18, 0x0a,
	0x13, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x52, 0x54, 0x5f, 0x42, 0x45, 0x41, 0x54,
	0x5f, 0x52, 0x53, 0x50, 0x10, 0xea, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x47, 0x41, 0x54, 0x45, 0x5f,
	0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x4e, 0x54,
	0x46, 0x10, 0xec, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4b, 0x49, 0x43,
	0x4b, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xed, 0x07, 0x12, 0x11, 0x0a, 0x0c,
	0x43, 0x4d, 0x44, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xcb, 0x08, 0x12,
	0x14, 0x0a, 0x0f, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x42, 0x45, 0x47,
	0x49, 0x4e, 0x10, 0xcc, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xcd, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4d, 0x44,
	0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xce, 0x08, 0x12, 0x13, 0x0a,
	0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10,
	0xcf, 0x08, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54,
	0x5f, 0x52, 0x53, 0x50, 0x10, 0xd0, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xd1, 0x08, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xd2,
	0x08, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x45,
	0x4e, 0x44, 0x10, 0xaf, 0x09, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c,
	0x4c, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xd0, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d,
	0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xd1, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xd2, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x45, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd3, 0x0f, 0x12, 0x1a,
	0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x45, 0x52, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xd4, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d,
	0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xd5, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xd6, 0x0f, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xd7, 0x0f, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xd8, 0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d,
	0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x42, 0x55, 0x59, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd9, 0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43,
	0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x42, 0x55, 0x59,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xda, 0x0f, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x4d, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xdb, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52,
	0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xdc, 0x0f, 0x12, 0x18, 0x0a, 0x13,
	0x43, 0x4d, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x4e, 0x54, 0x46, 0x10, 0xde, 0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x52, 0x45, 0x51, 0x10, 0xdf, 0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xe0, 0x0f, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f,
	0x47, 0x45, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xe1, 0x0f, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53,
	0x50, 0x10, 0xe2, 0x0f, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x4d, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x5f,
	0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe3, 0x0f, 0x12, 0x15, 0x0a, 0x10, 0x43,
	0x4d, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xe4, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x58, 0x50, 0x5f, 0x4c, 0x45,
	0x56, 0x45, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xe6,
	0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x45,
	0x4e, 0x54, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe7, 0x0f,
	0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x45, 0x4e,
	0x54, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xe8, 0x0f, 0x12,
	0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x4d, 0x55, 0x4c,
	0x54, 0x49, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe9, 0x0f, 0x12, 0x1c, 0x0a,
	0x17, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49,
	0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xea, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x43,
	0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xeb, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d,
	0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xec, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4d, 0x44,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49, 0x47, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xed, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x43,
	0x4d, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49,
	0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xee, 0x0f, 0x12, 0x20, 0x0a,
	0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x44, 0x5f,
	0x52, 0x49, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xef, 0x0f, 0x12,
	0x20, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x52, 0x4f,
	0x44, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xf0,
	0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10,
	0xf1, 0x0f, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59,
	0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xf2, 0x0f, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46,
	0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x41, 0x56, 0x41, 0x54, 0x41, 0x52, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xf3, 0x0f, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x4f,
	0x44, 0x49, 0x46, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x41, 0x56, 0x41, 0x54,
	0x41, 0x52, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xf4, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x46,
	0x52, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xf5, 0x0f, 0x12, 0x20, 0x0a, 0x1b, 0x43,
	0x4d, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52,
	0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xf6, 0x0f, 0x12, 0x1f, 0x0a,
	0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xf8, 0x0f, 0x12, 0x1a,
	0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xf9, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d,
	0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f,
	0x52, 0x53, 0x50, 0x10, 0xfa, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e,
	0x54, 0x46, 0x10, 0xfc, 0x0f, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xfd, 0x0f,
	0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50,
	0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xfe, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43,
	0x4d, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x10, 0xff, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0x80, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x5f, 0x54,
	0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x81, 0x10, 0x12, 0x19,
	0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52,
	0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x82, 0x10, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44,
	0x5f, 0x52, 0x45, 0x51, 0x10, 0x83, 0x10, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52,
	0x53, 0x50, 0x10, 0x84, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x85, 0x10,
	0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50,
	0x5f, 0x42, 0x41, 0x47, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x86, 0x10, 0x12, 0x1c, 0x0a, 0x17, 0x43,
	0x4d, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42,
	0x41, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x87, 0x10, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47,
	0x5f, 0x52, 0x53, 0x50, 0x10, 0x88, 0x10, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x54,
	0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x5f, 0x42, 0x55,
	0x59, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x89, 0x10, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f,
	0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x5f, 0x42,
	0x55, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x8a, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44,
	0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0x8b, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x52, 0x49, 0x50,
	0x5f, 0x42, 0x41, 0x47, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x8c, 0x10, 0x12,
	0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x4f, 0x52,
	0x42, 0x49, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x8d, 0x10, 0x12,
	0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x4f, 0x52,
	0x42, 0x49, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x8e, 0x10, 0x12,
	0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x8f, 0x10, 0x12, 0x1b, 0x0a, 0x16,
	0x43, 0x4d, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x90, 0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x95, 0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x96, 0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x97, 0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x98, 0x10, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47,
	0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x99, 0x10, 0x12, 0x22, 0x0a,
	0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x53, 0x54,
	0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x9a,
	0x10, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x54, 0x52,
	0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x9b, 0x10, 0x12, 0x1a, 0x0a,
	0x15, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52,
	0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x9c, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44,
	0x5f, 0x50, 0x55, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0x9d, 0x10, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x55, 0x54, 0x5f,
	0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x9e, 0x10, 0x12,
	0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x52,
	0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x9f, 0x10, 0x12, 0x1c, 0x0a,
	0x17, 0x43, 0x4d, 0x44, 0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x52, 0x49, 0x50,
	0x5f, 0x42, 0x41, 0x47, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xa0, 0x10, 0x12, 0x1b, 0x0a, 0x16, 0x43,
	0x4d, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49,
	0x47, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xa2, 0x10, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f,
	0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xa3, 0x10,
	0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xa4, 0x10, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f,
	0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x52, 0x49,
	0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xa5, 0x10, 0x12, 0x22, 0x0a, 0x1d,
	0x43, 0x4d, 0x44, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xa6, 0x10,
	0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x48, 0x45, 0x41,
	0x50, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xa7, 0x10, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f,
	0x49, 0x54, 0x45, 0x4d, 0x5f, 0x48, 0x45, 0x41, 0x50, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xa8, 0x10,
	0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x48, 0x45, 0x41,
	0x50, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xaa, 0x10, 0x12,
	0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x49,
	0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xab, 0x10, 0x12, 0x1b, 0x0a, 0x16,
	0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52,
	0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xac, 0x10, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44,
	0x5f, 0x54, 0x45, 0x4d, 0x50, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52,
	0x45, 0x51, 0x10, 0xad, 0x10, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x45, 0x4d,
	0x50, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xae,
	0x10, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x47, 0x55, 0x49, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xaf, 0x10, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4d,
	0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x47, 0x55,
	0x49, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xb0, 0x10, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e,
	0x5f, 0x52, 0x45, 0x51, 0x10, 0xb1, 0x10, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x48,
	0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55, 0x53, 0x5f, 0x4c,
	0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xb2, 0x10, 0x12, 0x29, 0x0a, 0x24, 0x43,
	0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f,
	0x55, 0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xb3, 0x10, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41,
	0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55, 0x53, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xb4,
	0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xb5,
	0x10, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xb6,
	0x10, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x5f, 0x52,
	0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xb7, 0x10, 0x12, 0x1a, 0x0a,
	0x15, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xb8, 0x10, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4d, 0x44,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f,
	0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xb9, 0x10, 0x12, 0x23,
	0x0a, 0x1e, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52,
	0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xba, 0x10, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x50,
	0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x44,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xbb, 0x10, 0x12, 0x26, 0x0a, 0x21, 0x43,
	0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4d, 0x4f,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xbc, 0x10, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45,
	0x52, 0x5f, 0x52, 0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x4e, 0x54, 0x46, 0x10, 0xbe, 0x10, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x48,
	0x41, 0x4c, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45,
	0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xbf, 0x10, 0x12, 0x24, 0x0a,
	0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59,
	0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xc0, 0x10, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x44, 0x4b, 0x45, 0x59,
	0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xc1, 0x10,
	0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x44, 0x4b, 0x45, 0x59, 0x5f, 0x45, 0x58,
	0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc2, 0x10, 0x12, 0x21, 0x0a,
	0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x53, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xc3, 0x10,
	0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xc4, 0x10, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f,
	0x47, 0x45, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10,
	0xc5, 0x10, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc6,
	0x10, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4f,
	0x4e, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xc7, 0x10, 0x12,
	0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4f, 0x4e, 0x44,
	0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc8, 0x10, 0x12, 0x20, 0x0a,
	0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x53,
	0x54, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xc9, 0x10, 0x12,
	0x20, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4f, 0x4e, 0x44,
	0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xca,
	0x10, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x4e,
	0x44, 0x10, 0xb7, 0x17, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54,
	0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xb8, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0xb9, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xba, 0x17, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x42, 0x53, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xbc, 0x17, 0x12,
	0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x50, 0x4f, 0x54,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xbd, 0x17, 0x12, 0x1e, 0x0a, 0x19,
	0x43, 0x4d, 0x44, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x42, 0x53, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xbe, 0x17, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x4d, 0x44, 0x5f, 0x54, 0x48, 0x52, 0x4f, 0x57, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xbf, 0x17, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x48, 0x52, 0x4f,
	0x57, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc0, 0x17, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xc1, 0x17, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc2, 0x17, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x4d, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xc3, 0x17, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc4, 0x17, 0x12, 0x16, 0x0a, 0x11,
	0x43, 0x4d, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xc5, 0x17, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x54,
	0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc6, 0x17, 0x12, 0x19, 0x0a, 0x14,
	0x43, 0x4d, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x42, 0x53,
	0x5f, 0x4e, 0x54, 0x46, 0x10, 0xc8, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xc9, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xca,
	0x17, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x42, 0x53, 0x5f, 0x4e, 0x54,
	0x46, 0x10, 0xcc, 0x17, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x10, 0xcd, 0x17, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f,
	0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xce, 0x17, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45,
	0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10,
	0xcf, 0x17, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45,
	0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xd0, 0x17, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f,
	0x4f, 0x4d, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd1, 0x17, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4d, 0x44,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c,
	0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xd2, 0x17,
	0x12, 0x18, 0x0a, 0x13, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x53,
	0x50, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd3, 0x17, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x4d,
	0x44, 0x5f, 0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x52, 0x53,
	0x50, 0x10, 0xd4, 0x17, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x54,
	0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x4e, 0x54, 0x46,
	0x10, 0xd5, 0x17, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47,
	0x59, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xd6, 0x17, 0x12,
	0x1d, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x42, 0x41, 0x54, 0x54,
	0x4c, 0x45, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd7, 0x17, 0x12, 0x1d,
	0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c,
	0x45, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xd8, 0x17, 0x12, 0x1b, 0x0a,
	0x16, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x4f, 0x44, 0x5f,
	0x52, 0x49, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xd9, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d,
	0x44, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x52, 0x49, 0x47,
	0x5f, 0x52, 0x53, 0x50, 0x10, 0xda, 0x17, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x50,
	0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x5f, 0x43, 0x4f, 0x53,
	0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xdb, 0x17, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f,
	0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x5f, 0x43, 0x4f,
	0x53, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xdc, 0x17, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44,
	0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x56,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xdd, 0x17, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d,
	0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xde, 0x17, 0x12, 0x22, 0x0a, 0x1d, 0x43,
	0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f,
	0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x53, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xdf, 0x17, 0x12,
	0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x48, 0x4f, 0x4f, 0x4b,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe0, 0x17, 0x12, 0x1c, 0x0a,
	0x17, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xe1, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43,
	0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4e,
	0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe2, 0x17, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f,
	0x53, 0x50, 0x4f, 0x54, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x52,
	0x53, 0x50, 0x10, 0xe3, 0x17, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x50, 0x4f,
	0x54, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x9b, 0x18, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x4d, 0x44, 0x5f,
	0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x80, 0x19, 0x12, 0x1e,
	0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x57, 0x45, 0x41, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x81, 0x19, 0x12, 0x1e,
	0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x57, 0x45, 0x41, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x82, 0x19, 0x12, 0x1f,
	0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x47, 0x41, 0x4d, 0x45, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x83, 0x19, 0x12,
	0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x47, 0x45, 0x54,
	0x5f, 0x47, 0x41, 0x4d, 0x45, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x84, 0x19,
	0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x47, 0x45,
	0x54, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x51,
	0x10, 0x85, 0x19, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f,
	0x52, 0x53, 0x50, 0x10, 0x86, 0x19, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f,
	0x52, 0x4c, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0x87, 0x19, 0x12, 0x12, 0x0a,
	0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xe3,
	0x19, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x42, 0x45, 0x47, 0x49,
	0x4e, 0x10, 0xe4, 0x19, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xe5, 0x19, 0x12, 0x17, 0x0a,
	0x12, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f,
	0x52, 0x53, 0x50, 0x10, 0xe6, 0x19, 0x12, 0x0f, 0x0a, 0x0a, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d,
	0x5f, 0x45, 0x4e, 0x44, 0x10, 0xc7, 0x1a, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x50,
	0x41, 0x59, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xc8, 0x1a, 0x12, 0x26, 0x0a, 0x21, 0x43,
	0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x55,
	0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x51,
	0x10, 0xc9, 0x1a, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xca, 0x1a, 0x12, 0x27, 0x0a, 0x22, 0x43,
	0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x50,
	0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xcb, 0x1a, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xcc, 0x1a, 0x12, 0x25, 0x0a,
	0x20, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x50,
	0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xcd, 0x1a, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xce, 0x1a, 0x12, 0x10, 0x0a, 0x0b, 0x43,
	0x4d, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xab, 0x1b, 0x12, 0x13, 0x0a,
	0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10,
	0xac, 0x1b, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xad, 0x1b, 0x12, 0x1a,
	0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xae, 0x1b, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x4d,
	0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x54,
	0x46, 0x10, 0xb0, 0x1b, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xb1, 0x1b, 0x12, 0x18,
	0x0a, 0x13, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xb2, 0x1b, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45,
	0x51, 0x10, 0xb9, 0x1b, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xba, 0x1b,
	0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0xbb, 0x1b, 0x12, 0x21, 0x0a, 0x1c,
	0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f,
	0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xbd, 0x1b, 0x12,
	0x21, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0xbe, 0x1b, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xbf, 0x1b, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x4d, 0x44,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xc0, 0x1b,
	0x12, 0x15, 0x0a, 0x10, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x4e, 0x54, 0x46, 0x10, 0xc1, 0x1b, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x10, 0xc3, 0x1b, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x53, 0x50,
	0x10, 0xc4, 0x1b, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x45, 0x4e, 0x44, 0x10, 0x8f, 0x1c, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53,
	0x47, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x90, 0x1c, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d,
	0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x91, 0x1c, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d,
	0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x92, 0x1c, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d,
	0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x52, 0x45, 0x51, 0x10, 0x93, 0x1c, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53,
	0x47, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x53, 0x50, 0x10,
	0x94, 0x1c, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x4c,
	0x41, 0x49, 0x4d, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43,
	0x48, 0x5f, 0x52, 0x45, 0x51, 0x10, 0x95, 0x1c, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4d, 0x44, 0x5f,
	0x4d, 0x53, 0x47, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44,
	0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x5f, 0x52, 0x53, 0x50, 0x10, 0x96, 0x1c, 0x12, 0x19,
	0x0a, 0x14, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x4e, 0x54, 0x46, 0x10, 0x98, 0x1c, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44, 0x43, 0x41, 0x53, 0x54, 0x10, 0x9a,
	0x1c, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x54, 0x46,
	0x10, 0x9c, 0x1c, 0x12, 0x10, 0x0a, 0x0b, 0x43, 0x4d, 0x44, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x45,
	0x4e, 0x44, 0x10, 0xf3, 0x1c, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x53, 0x59, 0x4e,
	0x43, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xf4, 0x1c, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d,
	0x44, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xd7, 0x1d, 0x12, 0x13, 0x0a,
	0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10,
	0xd8, 0x1d, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x10, 0xd9, 0x1d, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f,
	0x47, 0x45, 0x54, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x52, 0x53,
	0x50, 0x10, 0xda, 0x1d, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x4e, 0x4b,
	0x5f, 0x45, 0x4e, 0x44, 0x10, 0xbb, 0x1e, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x54,
	0x41, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xbc, 0x1e, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d,
	0x44, 0x5f, 0x54, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x52, 0x45, 0x51, 0x10, 0xbd, 0x1e, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x54,
	0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x52, 0x53,
	0x50, 0x10, 0xbe, 0x1e, 0x12, 0x0f, 0x0a, 0x0a, 0x43, 0x4d, 0x44, 0x5f, 0x54, 0x41, 0x5f, 0x45,
	0x4e, 0x44, 0x10, 0x9f, 0x1f, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xa0, 0x1f, 0x12, 0x22,
	0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49,
	0x54, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x10,
	0xa1, 0x1f, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f,
	0x52, 0x53, 0x50, 0x10, 0xa2, 0x1f, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x4c,
	0x41, 0x49, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x10, 0xa3, 0x1f, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4d,
	0x44, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59,
	0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x53, 0x50, 0x10, 0xa4, 0x1f, 0x12, 0x15,
	0x0a, 0x10, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x45,
	0x4e, 0x44, 0x10, 0x83, 0x20, 0x12, 0x0c, 0x0a, 0x07, 0x43, 0x4d, 0x44, 0x5f, 0x45, 0x4e, 0x44,
	0x10, 0x8f, 0x4e, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66,
	0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e,
	0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_msgid_proto_rawDescOnce sync.Once
	file_msgid_proto_rawDescData = file_msgid_proto_rawDesc
)

func file_msgid_proto_rawDescGZIP() []byte {
	file_msgid_proto_rawDescOnce.Do(func() {
		file_msgid_proto_rawDescData = protoimpl.X.CompressGZIP(file_msgid_proto_rawDescData)
	})
	return file_msgid_proto_rawDescData
}

var file_msgid_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_msgid_proto_goTypes = []interface{}{
	(MsgID)(0), // 0: common.MsgID
}
var file_msgid_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_msgid_proto_init() }
func file_msgid_proto_init() {
	if File_msgid_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_msgid_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_msgid_proto_goTypes,
		DependencyIndexes: file_msgid_proto_depIdxs,
		EnumInfos:         file_msgid_proto_enumTypes,
	}.Build()
	File_msgid_proto = out.File
	file_msgid_proto_rawDesc = nil
	file_msgid_proto_goTypes = nil
	file_msgid_proto_depIdxs = nil
}
