// 枚举

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: enum.proto

package commonPB

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 平台类型
type PLATFORM_TYPE int32

const (
	PLATFORM_TYPE_PT_INIT    PLATFORM_TYPE = 0
	PLATFORM_TYPE_PT_UNITY   PLATFORM_TYPE = 1 // unity编辑器
	PLATFORM_TYPE_PT_IOS     PLATFORM_TYPE = 2 // iOS平台
	PLATFORM_TYPE_PT_ANDROID PLATFORM_TYPE = 3 // Android平台
	PLATFORM_TYPE_PT_WINDOWS PLATFORM_TYPE = 4 // Windows平台
	PLATFORM_TYPE_PT_MINI    PLATFORM_TYPE = 5 // 小游戏平台
	PLATFORM_TYPE_PT_H5      PLATFORM_TYPE = 6 // H5 Web平台
	PLATFORM_TYPE_PT_WEAR    PLATFORM_TYPE = 7 // 智能穿戴平台
	PLATFORM_TYPE_PT_CONSOLE PLATFORM_TYPE = 8 // 主机游戏平台
)

// Enum value maps for PLATFORM_TYPE.
var (
	PLATFORM_TYPE_name = map[int32]string{
		0: "PT_INIT",
		1: "PT_UNITY",
		2: "PT_IOS",
		3: "PT_ANDROID",
		4: "PT_WINDOWS",
		5: "PT_MINI",
		6: "PT_H5",
		7: "PT_WEAR",
		8: "PT_CONSOLE",
	}
	PLATFORM_TYPE_value = map[string]int32{
		"PT_INIT":    0,
		"PT_UNITY":   1,
		"PT_IOS":     2,
		"PT_ANDROID": 3,
		"PT_WINDOWS": 4,
		"PT_MINI":    5,
		"PT_H5":      6,
		"PT_WEAR":    7,
		"PT_CONSOLE": 8,
	}
)

func (x PLATFORM_TYPE) Enum() *PLATFORM_TYPE {
	p := new(PLATFORM_TYPE)
	*p = x
	return p
}

func (x PLATFORM_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PLATFORM_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[0].Descriptor()
}

func (PLATFORM_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[0]
}

func (x PLATFORM_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PLATFORM_TYPE.Descriptor instead.
func (PLATFORM_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{0}
}

// 产品ID
type PRODUCT_ID int32

const (
	PRODUCT_ID_PID_INIT   PRODUCT_ID = 0
	PRODUCT_ID_PID_FISHER PRODUCT_ID = 1 // 钓鱼产品
)

// Enum value maps for PRODUCT_ID.
var (
	PRODUCT_ID_name = map[int32]string{
		0: "PID_INIT",
		1: "PID_FISHER",
	}
	PRODUCT_ID_value = map[string]int32{
		"PID_INIT":   0,
		"PID_FISHER": 1,
	}
)

func (x PRODUCT_ID) Enum() *PRODUCT_ID {
	p := new(PRODUCT_ID)
	*p = x
	return p
}

func (x PRODUCT_ID) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PRODUCT_ID) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[1].Descriptor()
}

func (PRODUCT_ID) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[1]
}

func (x PRODUCT_ID) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PRODUCT_ID.Descriptor instead.
func (PRODUCT_ID) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{1}
}

// 渠道类型
type CHANNEL_TYPE int32

const (
	CHANNEL_TYPE_CT_INIT     CHANNEL_TYPE = 0
	CHANNEL_TYPE_CT_MASTER   CHANNEL_TYPE = 1001 // 研发
	CHANNEL_TYPE_CT_GOOGLE   CHANNEL_TYPE = 1002 // Google渠道包
	CHANNEL_TYPE_CT_APPSTORE CHANNEL_TYPE = 1003 // 苹果包
	CHANNEL_TYPE_CT_PLAN     CHANNEL_TYPE = 2001 // 内部策划渠道
)

// Enum value maps for CHANNEL_TYPE.
var (
	CHANNEL_TYPE_name = map[int32]string{
		0:    "CT_INIT",
		1001: "CT_MASTER",
		1002: "CT_GOOGLE",
		1003: "CT_APPSTORE",
		2001: "CT_PLAN",
	}
	CHANNEL_TYPE_value = map[string]int32{
		"CT_INIT":     0,
		"CT_MASTER":   1001,
		"CT_GOOGLE":   1002,
		"CT_APPSTORE": 1003,
		"CT_PLAN":     2001,
	}
)

func (x CHANNEL_TYPE) Enum() *CHANNEL_TYPE {
	p := new(CHANNEL_TYPE)
	*p = x
	return p
}

func (x CHANNEL_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CHANNEL_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[2].Descriptor()
}

func (CHANNEL_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[2]
}

func (x CHANNEL_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CHANNEL_TYPE.Descriptor instead.
func (CHANNEL_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{2}
}

// 环境类型
type ENV_TYPE int32

const (
	ENV_TYPE_ET_INIT     ENV_TYPE = 0
	ENV_TYPE_ET_DEV      ENV_TYPE = 1 // 开发
	ENV_TYPE_ET_TEST     ENV_TYPE = 2 // 测试
	ENV_TYPE_ET_PRE      ENV_TYPE = 3 // 预发布
	ENV_TYPE_ET_PROD     ENV_TYPE = 4 // 生产环境
	ENV_TYPE_ET_AUDIT    ENV_TYPE = 5 // 审核环境
	ENV_TYPE_ET_OPTIMIZE ENV_TYPE = 6 // 单用途，优化环境
)

// Enum value maps for ENV_TYPE.
var (
	ENV_TYPE_name = map[int32]string{
		0: "ET_INIT",
		1: "ET_DEV",
		2: "ET_TEST",
		3: "ET_PRE",
		4: "ET_PROD",
		5: "ET_AUDIT",
		6: "ET_OPTIMIZE",
	}
	ENV_TYPE_value = map[string]int32{
		"ET_INIT":     0,
		"ET_DEV":      1,
		"ET_TEST":     2,
		"ET_PRE":      3,
		"ET_PROD":     4,
		"ET_AUDIT":    5,
		"ET_OPTIMIZE": 6,
	}
)

func (x ENV_TYPE) Enum() *ENV_TYPE {
	p := new(ENV_TYPE)
	*p = x
	return p
}

func (x ENV_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ENV_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[3].Descriptor()
}

func (ENV_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[3]
}

func (x ENV_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ENV_TYPE.Descriptor instead.
func (ENV_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{3}
}

// 网络类型
type NETWORK_TYPE int32

const (
	NETWORK_TYPE_NT_UNKNOWN NETWORK_TYPE = 0 // 未知网络
	NETWORK_TYPE_NT_NONE    NETWORK_TYPE = 1 // 未能获取到
	NETWORK_TYPE_NT_MOBILE  NETWORK_TYPE = 2 // 移动网络
	NETWORK_TYPE_NT_LOCAL   NETWORK_TYPE = 3 // 局域网
	NETWORK_TYPE_NT_OTHER   NETWORK_TYPE = 4 // 其他
)

// Enum value maps for NETWORK_TYPE.
var (
	NETWORK_TYPE_name = map[int32]string{
		0: "NT_UNKNOWN",
		1: "NT_NONE",
		2: "NT_MOBILE",
		3: "NT_LOCAL",
		4: "NT_OTHER",
	}
	NETWORK_TYPE_value = map[string]int32{
		"NT_UNKNOWN": 0,
		"NT_NONE":    1,
		"NT_MOBILE":  2,
		"NT_LOCAL":   3,
		"NT_OTHER":   4,
	}
)

func (x NETWORK_TYPE) Enum() *NETWORK_TYPE {
	p := new(NETWORK_TYPE)
	*p = x
	return p
}

func (x NETWORK_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NETWORK_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[4].Descriptor()
}

func (NETWORK_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[4]
}

func (x NETWORK_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NETWORK_TYPE.Descriptor instead.
func (NETWORK_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{4}
}

// 语言码映射枚举类型 （支持适配的国际化版本）
type LANGUAGE_TYPE int32

const (
	LANGUAGE_TYPE_LT_INIT  LANGUAGE_TYPE = 0 // 缺省
	LANGUAGE_TYPE_LT_EN_US LANGUAGE_TYPE = 1 // 英语
	LANGUAGE_TYPE_LT_ZH_CN LANGUAGE_TYPE = 2 // 简体中文
	LANGUAGE_TYPE_LT_ZH_TW LANGUAGE_TYPE = 3 // 繁体中文
	LANGUAGE_TYPE_LT_DE_DE LANGUAGE_TYPE = 4 // 德语
)

// Enum value maps for LANGUAGE_TYPE.
var (
	LANGUAGE_TYPE_name = map[int32]string{
		0: "LT_INIT",
		1: "LT_EN_US",
		2: "LT_ZH_CN",
		3: "LT_ZH_TW",
		4: "LT_DE_DE",
	}
	LANGUAGE_TYPE_value = map[string]int32{
		"LT_INIT":  0,
		"LT_EN_US": 1,
		"LT_ZH_CN": 2,
		"LT_ZH_TW": 3,
		"LT_DE_DE": 4,
	}
)

func (x LANGUAGE_TYPE) Enum() *LANGUAGE_TYPE {
	p := new(LANGUAGE_TYPE)
	*p = x
	return p
}

func (x LANGUAGE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LANGUAGE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[5].Descriptor()
}

func (LANGUAGE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[5]
}

func (x LANGUAGE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LANGUAGE_TYPE.Descriptor instead.
func (LANGUAGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{5}
}

// 灰度状态
type GRAY_STATUS int32

const (
	GRAY_STATUS_GS_None   GRAY_STATUS = 0 // none
	GRAY_STATUS_GS_Normal GRAY_STATUS = 1 // 正常
	GRAY_STATUS_GS_Gray   GRAY_STATUS = 2 // 灰度
)

// Enum value maps for GRAY_STATUS.
var (
	GRAY_STATUS_name = map[int32]string{
		0: "GS_None",
		1: "GS_Normal",
		2: "GS_Gray",
	}
	GRAY_STATUS_value = map[string]int32{
		"GS_None":   0,
		"GS_Normal": 1,
		"GS_Gray":   2,
	}
)

func (x GRAY_STATUS) Enum() *GRAY_STATUS {
	p := new(GRAY_STATUS)
	*p = x
	return p
}

func (x GRAY_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GRAY_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[6].Descriptor()
}

func (GRAY_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[6]
}

func (x GRAY_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GRAY_STATUS.Descriptor instead.
func (GRAY_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{6}
}

// 灰度策略
type GRAY_STRATEGY int32

const (
	GRAY_STRATEGY_GS_NONE        GRAY_STRATEGY = 0 // 无
	GRAY_STRATEGY_GS_LOCATION    GRAY_STRATEGY = 1 // 地理位置(ip)
	GRAY_STRATEGY_GS_TAIL_NUMBER GRAY_STRATEGY = 2 // 尾号
	GRAY_STRATEGY_GS_TAG         GRAY_STRATEGY = 3 // 标签
	GRAY_STRATEGY_GS_VERSION     GRAY_STRATEGY = 4 // 版本
	GRAY_STRATEGY_GS_WHITE_LIST  GRAY_STRATEGY = 5 // 白名单
)

// Enum value maps for GRAY_STRATEGY.
var (
	GRAY_STRATEGY_name = map[int32]string{
		0: "GS_NONE",
		1: "GS_LOCATION",
		2: "GS_TAIL_NUMBER",
		3: "GS_TAG",
		4: "GS_VERSION",
		5: "GS_WHITE_LIST",
	}
	GRAY_STRATEGY_value = map[string]int32{
		"GS_NONE":        0,
		"GS_LOCATION":    1,
		"GS_TAIL_NUMBER": 2,
		"GS_TAG":         3,
		"GS_VERSION":     4,
		"GS_WHITE_LIST":  5,
	}
)

func (x GRAY_STRATEGY) Enum() *GRAY_STRATEGY {
	p := new(GRAY_STRATEGY)
	*p = x
	return p
}

func (x GRAY_STRATEGY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GRAY_STRATEGY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[7].Descriptor()
}

func (GRAY_STRATEGY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[7]
}

func (x GRAY_STRATEGY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GRAY_STRATEGY.Descriptor instead.
func (GRAY_STRATEGY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{7}
}

// 地理位置类型
type LOCATION_TYPE int32

const (
	LOCATION_TYPE_LT_NONE   LOCATION_TYPE = 0 // 无
	LOCATION_TYPE_LT_FORBID LOCATION_TYPE = 1 // 屏蔽
	LOCATION_TYPE_LT_GRAY   LOCATION_TYPE = 2 // 灰度
)

// Enum value maps for LOCATION_TYPE.
var (
	LOCATION_TYPE_name = map[int32]string{
		0: "LT_NONE",
		1: "LT_FORBID",
		2: "LT_GRAY",
	}
	LOCATION_TYPE_value = map[string]int32{
		"LT_NONE":   0,
		"LT_FORBID": 1,
		"LT_GRAY":   2,
	}
)

func (x LOCATION_TYPE) Enum() *LOCATION_TYPE {
	p := new(LOCATION_TYPE)
	*p = x
	return p
}

func (x LOCATION_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LOCATION_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[8].Descriptor()
}

func (LOCATION_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[8]
}

func (x LOCATION_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LOCATION_TYPE.Descriptor instead.
func (LOCATION_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{8}
}

// 功能隐藏类型
type FEATURE_HIDE_TYPE int32

const (
	FEATURE_HIDE_TYPE_FHT_NONE     FEATURE_HIDE_TYPE = 0 // 不隐藏
	FEATURE_HIDE_TYPE_FHT_HIDE     FEATURE_HIDE_TYPE = 1 // 隐藏
	FEATURE_HIDE_TYPE_FHT_NOT_OPEN FEATURE_HIDE_TYPE = 2 // 暂未开放
)

// Enum value maps for FEATURE_HIDE_TYPE.
var (
	FEATURE_HIDE_TYPE_name = map[int32]string{
		0: "FHT_NONE",
		1: "FHT_HIDE",
		2: "FHT_NOT_OPEN",
	}
	FEATURE_HIDE_TYPE_value = map[string]int32{
		"FHT_NONE":     0,
		"FHT_HIDE":     1,
		"FHT_NOT_OPEN": 2,
	}
)

func (x FEATURE_HIDE_TYPE) Enum() *FEATURE_HIDE_TYPE {
	p := new(FEATURE_HIDE_TYPE)
	*p = x
	return p
}

func (x FEATURE_HIDE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FEATURE_HIDE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[9].Descriptor()
}

func (FEATURE_HIDE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[9]
}

func (x FEATURE_HIDE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FEATURE_HIDE_TYPE.Descriptor instead.
func (FEATURE_HIDE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{9}
}

// 白名单功能类型
type WHITE_FUNC_TYPE int32

const (
	WHITE_FUNC_TYPE_WFT_NONE     WHITE_FUNC_TYPE = 0 // 无
	WHITE_FUNC_TYPE_WFT_LOGIN    WHITE_FUNC_TYPE = 1 // 登录
	WHITE_FUNC_TYPE_WFT_RECHARGE WHITE_FUNC_TYPE = 2 // 充值
)

// Enum value maps for WHITE_FUNC_TYPE.
var (
	WHITE_FUNC_TYPE_name = map[int32]string{
		0: "WFT_NONE",
		1: "WFT_LOGIN",
		2: "WFT_RECHARGE",
	}
	WHITE_FUNC_TYPE_value = map[string]int32{
		"WFT_NONE":     0,
		"WFT_LOGIN":    1,
		"WFT_RECHARGE": 2,
	}
)

func (x WHITE_FUNC_TYPE) Enum() *WHITE_FUNC_TYPE {
	p := new(WHITE_FUNC_TYPE)
	*p = x
	return p
}

func (x WHITE_FUNC_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WHITE_FUNC_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[10].Descriptor()
}

func (WHITE_FUNC_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[10]
}

func (x WHITE_FUNC_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WHITE_FUNC_TYPE.Descriptor instead.
func (WHITE_FUNC_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{10}
}

// 登录方式
type LOGIN_TYPE int32

const (
	LOGIN_TYPE_LT_Unknown  LOGIN_TYPE = 0 // 未知
	LOGIN_TYPE_LT_VISITOR  LOGIN_TYPE = 1 // 游客登录
	LOGIN_TYPE_LT_TOKEN    LOGIN_TYPE = 2 // token 登录
	LOGIN_TYPE_LT_FACEBOOK LOGIN_TYPE = 3 // FB登录
	LOGIN_TYPE_LT_APPLE    LOGIN_TYPE = 4 // 苹果账号登录
	LOGIN_TYPE_LT_PASSWORD LOGIN_TYPE = 5 // 账号密码登录
	LOGIN_TYPE_LT_GOOGLE   LOGIN_TYPE = 6 // Google登录
)

// Enum value maps for LOGIN_TYPE.
var (
	LOGIN_TYPE_name = map[int32]string{
		0: "LT_Unknown",
		1: "LT_VISITOR",
		2: "LT_TOKEN",
		3: "LT_FACEBOOK",
		4: "LT_APPLE",
		5: "LT_PASSWORD",
		6: "LT_GOOGLE",
	}
	LOGIN_TYPE_value = map[string]int32{
		"LT_Unknown":  0,
		"LT_VISITOR":  1,
		"LT_TOKEN":    2,
		"LT_FACEBOOK": 3,
		"LT_APPLE":    4,
		"LT_PASSWORD": 5,
		"LT_GOOGLE":   6,
	}
)

func (x LOGIN_TYPE) Enum() *LOGIN_TYPE {
	p := new(LOGIN_TYPE)
	*p = x
	return p
}

func (x LOGIN_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LOGIN_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[11].Descriptor()
}

func (LOGIN_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[11]
}

func (x LOGIN_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LOGIN_TYPE.Descriptor instead.
func (LOGIN_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{11}
}

type ACC_STATUS int32

const (
	ACC_STATUS_AS_NORMAL    ACC_STATUS = 0 // 正常
	ACC_STATUS_AS_BAN       ACC_STATUS = 1 // 冻结，封号
	ACC_STATUS_AS_LOGOFF    ACC_STATUS = 2 // 注销
	ACC_STATUS_AS_FORBIDDEN ACC_STATUS = 3 // 禁止游戏
)

// Enum value maps for ACC_STATUS.
var (
	ACC_STATUS_name = map[int32]string{
		0: "AS_NORMAL",
		1: "AS_BAN",
		2: "AS_LOGOFF",
		3: "AS_FORBIDDEN",
	}
	ACC_STATUS_value = map[string]int32{
		"AS_NORMAL":    0,
		"AS_BAN":       1,
		"AS_LOGOFF":    2,
		"AS_FORBIDDEN": 3,
	}
)

func (x ACC_STATUS) Enum() *ACC_STATUS {
	p := new(ACC_STATUS)
	*p = x
	return p
}

func (x ACC_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ACC_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[12].Descriptor()
}

func (ACC_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[12]
}

func (x ACC_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ACC_STATUS.Descriptor instead.
func (ACC_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{12}
}

// 账户类型
type ACC_TYPE int32

const (
	ACC_TYPE_AT_INIT     ACC_TYPE = 0
	ACC_TYPE_AT_VISITOR  ACC_TYPE = 1 // 游客
	ACC_TYPE_AT_PASSWORD ACC_TYPE = 2 // 账号密码
	ACC_TYPE_AT_TOKEN    ACC_TYPE = 3 // token
	ACC_TYPE_AT_ROBOT    ACC_TYPE = 4 // 机器人
	// 第三方账号登录类型预留区间 （10, 100]
	ACC_TYPE_AT_FACEBOOK  ACC_TYPE = 11  // facebook账号
	ACC_TYPE_AT_APPLE     ACC_TYPE = 12  // 苹果账号
	ACC_TYPE_AT_GOOGLE    ACC_TYPE = 13  // Google
	ACC_TYPE_AT_THIRD_END ACC_TYPE = 100 // 第三方账号登录类型预留区间上限
)

// Enum value maps for ACC_TYPE.
var (
	ACC_TYPE_name = map[int32]string{
		0:   "AT_INIT",
		1:   "AT_VISITOR",
		2:   "AT_PASSWORD",
		3:   "AT_TOKEN",
		4:   "AT_ROBOT",
		11:  "AT_FACEBOOK",
		12:  "AT_APPLE",
		13:  "AT_GOOGLE",
		100: "AT_THIRD_END",
	}
	ACC_TYPE_value = map[string]int32{
		"AT_INIT":      0,
		"AT_VISITOR":   1,
		"AT_PASSWORD":  2,
		"AT_TOKEN":     3,
		"AT_ROBOT":     4,
		"AT_FACEBOOK":  11,
		"AT_APPLE":     12,
		"AT_GOOGLE":    13,
		"AT_THIRD_END": 100,
	}
)

func (x ACC_TYPE) Enum() *ACC_TYPE {
	p := new(ACC_TYPE)
	*p = x
	return p
}

func (x ACC_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ACC_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[13].Descriptor()
}

func (ACC_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[13]
}

func (x ACC_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ACC_TYPE.Descriptor instead.
func (ACC_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{13}
}

// 封号原因类型
type BAN_ACC_REASON_TYPE int32

const (
	BAN_ACC_REASON_TYPE_BAR_TYPE_INIT        BAN_ACC_REASON_TYPE = 0
	BAN_ACC_REASON_TYPE_BAR_TYPE_REFUND_FREQ BAN_ACC_REASON_TYPE = 1 // 频繁退款
	BAN_ACC_REASON_TYPE_BAR_TYPE_GM          BAN_ACC_REASON_TYPE = 2 // GM操作
)

// Enum value maps for BAN_ACC_REASON_TYPE.
var (
	BAN_ACC_REASON_TYPE_name = map[int32]string{
		0: "BAR_TYPE_INIT",
		1: "BAR_TYPE_REFUND_FREQ",
		2: "BAR_TYPE_GM",
	}
	BAN_ACC_REASON_TYPE_value = map[string]int32{
		"BAR_TYPE_INIT":        0,
		"BAR_TYPE_REFUND_FREQ": 1,
		"BAR_TYPE_GM":          2,
	}
)

func (x BAN_ACC_REASON_TYPE) Enum() *BAN_ACC_REASON_TYPE {
	p := new(BAN_ACC_REASON_TYPE)
	*p = x
	return p
}

func (x BAN_ACC_REASON_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BAN_ACC_REASON_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[14].Descriptor()
}

func (BAN_ACC_REASON_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[14]
}

func (x BAN_ACC_REASON_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BAN_ACC_REASON_TYPE.Descriptor instead.
func (BAN_ACC_REASON_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{14}
}

// 登录方式
type LOGIN_MODEL int32

const (
	LOGIN_MODEL_LIM_SUCCESS LOGIN_MODEL = 0 // 正常登录
	LOGIN_MODEL_LIM_ANOTHER LOGIN_MODEL = 1 // 顶号登录
)

// Enum value maps for LOGIN_MODEL.
var (
	LOGIN_MODEL_name = map[int32]string{
		0: "LIM_SUCCESS",
		1: "LIM_ANOTHER",
	}
	LOGIN_MODEL_value = map[string]int32{
		"LIM_SUCCESS": 0,
		"LIM_ANOTHER": 1,
	}
)

func (x LOGIN_MODEL) Enum() *LOGIN_MODEL {
	p := new(LOGIN_MODEL)
	*p = x
	return p
}

func (x LOGIN_MODEL) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LOGIN_MODEL) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[15].Descriptor()
}

func (LOGIN_MODEL) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[15]
}

func (x LOGIN_MODEL) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LOGIN_MODEL.Descriptor instead.
func (LOGIN_MODEL) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{15}
}

// 下线方式
type LOGOUT_MODEL int32

const (
	LOGOUT_MODEL_LOM_SUCCESS    LOGOUT_MODEL = 0 // 主动下线
	LOGOUT_MODEL_LOM_DISCONNECT LOGOUT_MODEL = 1 // 断线
)

// Enum value maps for LOGOUT_MODEL.
var (
	LOGOUT_MODEL_name = map[int32]string{
		0: "LOM_SUCCESS",
		1: "LOM_DISCONNECT",
	}
	LOGOUT_MODEL_value = map[string]int32{
		"LOM_SUCCESS":    0,
		"LOM_DISCONNECT": 1,
	}
)

func (x LOGOUT_MODEL) Enum() *LOGOUT_MODEL {
	p := new(LOGOUT_MODEL)
	*p = x
	return p
}

func (x LOGOUT_MODEL) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LOGOUT_MODEL) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[16].Descriptor()
}

func (LOGOUT_MODEL) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[16]
}

func (x LOGOUT_MODEL) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LOGOUT_MODEL.Descriptor instead.
func (LOGOUT_MODEL) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{16}
}

// 踢人原因
type KICK_PLAYER_REASON int32

const (
	KICK_PLAYER_REASON_KPR_UNKNOWN  KICK_PLAYER_REASON = 0 // 未知原因
	KICK_PLAYER_REASON_KPR_DOWNTIME KICK_PLAYER_REASON = 1 // 停服维护
)

// Enum value maps for KICK_PLAYER_REASON.
var (
	KICK_PLAYER_REASON_name = map[int32]string{
		0: "KPR_UNKNOWN",
		1: "KPR_DOWNTIME",
	}
	KICK_PLAYER_REASON_value = map[string]int32{
		"KPR_UNKNOWN":  0,
		"KPR_DOWNTIME": 1,
	}
)

func (x KICK_PLAYER_REASON) Enum() *KICK_PLAYER_REASON {
	p := new(KICK_PLAYER_REASON)
	*p = x
	return p
}

func (x KICK_PLAYER_REASON) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KICK_PLAYER_REASON) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[17].Descriptor()
}

func (KICK_PLAYER_REASON) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[17]
}

func (x KICK_PLAYER_REASON) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KICK_PLAYER_REASON.Descriptor instead.
func (KICK_PLAYER_REASON) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{17}
}

// ************************************************************************
type USER_NOVICE_DONE_STATUS int32

const (
	USER_NOVICE_DONE_STATUS_UND_STATUS_INIT USER_NOVICE_DONE_STATUS = 0 // 未完成新手
	USER_NOVICE_DONE_STATUS_UND_STATUS_DONE USER_NOVICE_DONE_STATUS = 1 // 完成新手
)

// Enum value maps for USER_NOVICE_DONE_STATUS.
var (
	USER_NOVICE_DONE_STATUS_name = map[int32]string{
		0: "UND_STATUS_INIT",
		1: "UND_STATUS_DONE",
	}
	USER_NOVICE_DONE_STATUS_value = map[string]int32{
		"UND_STATUS_INIT": 0,
		"UND_STATUS_DONE": 1,
	}
)

func (x USER_NOVICE_DONE_STATUS) Enum() *USER_NOVICE_DONE_STATUS {
	p := new(USER_NOVICE_DONE_STATUS)
	*p = x
	return p
}

func (x USER_NOVICE_DONE_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (USER_NOVICE_DONE_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[18].Descriptor()
}

func (USER_NOVICE_DONE_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[18]
}

func (x USER_NOVICE_DONE_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use USER_NOVICE_DONE_STATUS.Descriptor instead.
func (USER_NOVICE_DONE_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{18}
}

// 用户初始信息
type USER_INIT_INFO int32

const (
	USER_INIT_INFO_UIF_UKNOWN USER_INIT_INFO = 0
	USER_INIT_INFO_UIF_FRAME  USER_INIT_INFO = 4010000 // 初始头像框
	USER_INIT_INFO_UIF_AVATAR USER_INIT_INFO = 4020000 // 初始头像
)

// Enum value maps for USER_INIT_INFO.
var (
	USER_INIT_INFO_name = map[int32]string{
		0:       "UIF_UKNOWN",
		4010000: "UIF_FRAME",
		4020000: "UIF_AVATAR",
	}
	USER_INIT_INFO_value = map[string]int32{
		"UIF_UKNOWN": 0,
		"UIF_FRAME":  4010000,
		"UIF_AVATAR": 4020000,
	}
)

func (x USER_INIT_INFO) Enum() *USER_INIT_INFO {
	p := new(USER_INIT_INFO)
	*p = x
	return p
}

func (x USER_INIT_INFO) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (USER_INIT_INFO) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[19].Descriptor()
}

func (USER_INIT_INFO) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[19]
}

func (x USER_INIT_INFO) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use USER_INIT_INFO.Descriptor instead.
func (USER_INIT_INFO) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{19}
}

// 年龄段定义
type USER_AGE int32

const (
	USER_AGE_UA_UNKNOWN  USER_AGE = 0   // 未知
	USER_AGE_UA_CHILD    USER_AGE = 8   // 幼年 （0,8)
	USER_AGE_UA_JUVENILE USER_AGE = 16  // 少年  [8,16)
	USER_AGE_UA_YOUNG    USER_AGE = 18  // 青少年 [16,18)
	USER_AGE_UA_ADULT    USER_AGE = 200 // 成年人  >= 18
)

// Enum value maps for USER_AGE.
var (
	USER_AGE_name = map[int32]string{
		0:   "UA_UNKNOWN",
		8:   "UA_CHILD",
		16:  "UA_JUVENILE",
		18:  "UA_YOUNG",
		200: "UA_ADULT",
	}
	USER_AGE_value = map[string]int32{
		"UA_UNKNOWN":  0,
		"UA_CHILD":    8,
		"UA_JUVENILE": 16,
		"UA_YOUNG":    18,
		"UA_ADULT":    200,
	}
)

func (x USER_AGE) Enum() *USER_AGE {
	p := new(USER_AGE)
	*p = x
	return p
}

func (x USER_AGE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (USER_AGE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[20].Descriptor()
}

func (USER_AGE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[20]
}

func (x USER_AGE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use USER_AGE.Descriptor instead.
func (USER_AGE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{20}
}

// 角色等级解锁功能（红点也使用）
type USER_MODULE_TYPE int32

const (
	USER_MODULE_TYPE_UMT_UNKNOWN        USER_MODULE_TYPE = 0  // 未知
	USER_MODULE_TYPE_UMT_FISH_CARD      USER_MODULE_TYPE = 1  // 鱼册
	USER_MODULE_TYPE_UMT_RANK           USER_MODULE_TYPE = 2  // 排行榜
	USER_MODULE_TYPE_UMT_WAREHOUSE      USER_MODULE_TYPE = 3  // 仓库
	USER_MODULE_TYPE_UMT_ACTIVITY       USER_MODULE_TYPE = 4  // 活动
	USER_MODULE_TYPE_UMT_CHAT           USER_MODULE_TYPE = 5  // 聊天
	USER_MODULE_TYPE_UMT_NOVICE         USER_MODULE_TYPE = 6  // 新手引导
	USER_MODULE_TYPE_UMT_MAIL           USER_MODULE_TYPE = 7  // 邮件
	USER_MODULE_TYPE_UMT_ACHIEVEMENT    USER_MODULE_TYPE = 8  // 成就
	USER_MODULE_TYPE_UMT_FB_LOGIN       USER_MODULE_TYPE = 9  // FB登录
	USER_MODULE_TYPE_UMT_GP_LOGIN       USER_MODULE_TYPE = 10 // GP 登录
	USER_MODULE_TYPE_UMT_TASK           USER_MODULE_TYPE = 11 // 任务
	USER_MODULE_TYPE_UMT_CHALLENGE_TASK USER_MODULE_TYPE = 12 // 挑战任务
	USER_MODULE_TYPE_UMT_HALUERSWAY     USER_MODULE_TYPE = 13 // 爆护之路
)

// Enum value maps for USER_MODULE_TYPE.
var (
	USER_MODULE_TYPE_name = map[int32]string{
		0:  "UMT_UNKNOWN",
		1:  "UMT_FISH_CARD",
		2:  "UMT_RANK",
		3:  "UMT_WAREHOUSE",
		4:  "UMT_ACTIVITY",
		5:  "UMT_CHAT",
		6:  "UMT_NOVICE",
		7:  "UMT_MAIL",
		8:  "UMT_ACHIEVEMENT",
		9:  "UMT_FB_LOGIN",
		10: "UMT_GP_LOGIN",
		11: "UMT_TASK",
		12: "UMT_CHALLENGE_TASK",
		13: "UMT_HALUERSWAY",
	}
	USER_MODULE_TYPE_value = map[string]int32{
		"UMT_UNKNOWN":        0,
		"UMT_FISH_CARD":      1,
		"UMT_RANK":           2,
		"UMT_WAREHOUSE":      3,
		"UMT_ACTIVITY":       4,
		"UMT_CHAT":           5,
		"UMT_NOVICE":         6,
		"UMT_MAIL":           7,
		"UMT_ACHIEVEMENT":    8,
		"UMT_FB_LOGIN":       9,
		"UMT_GP_LOGIN":       10,
		"UMT_TASK":           11,
		"UMT_CHALLENGE_TASK": 12,
		"UMT_HALUERSWAY":     13,
	}
)

func (x USER_MODULE_TYPE) Enum() *USER_MODULE_TYPE {
	p := new(USER_MODULE_TYPE)
	*p = x
	return p
}

func (x USER_MODULE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (USER_MODULE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[21].Descriptor()
}

func (USER_MODULE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[21]
}

func (x USER_MODULE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use USER_MODULE_TYPE.Descriptor instead.
func (USER_MODULE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{21}
}

// 邮件子模块
type RED_DOT_MAIL_SUB_TYPE int32

const (
	RED_DOT_MAIL_SUB_TYPE_RDMST_DEFAULT RED_DOT_MAIL_SUB_TYPE = 0 // 没有子模块就是默认
	RED_DOT_MAIL_SUB_TYPE_RDMST_NOTIFY  RED_DOT_MAIL_SUB_TYPE = 1 // 通知
	RED_DOT_MAIL_SUB_TYPE_RDMST_MAIL    RED_DOT_MAIL_SUB_TYPE = 2 // 邮件
)

// Enum value maps for RED_DOT_MAIL_SUB_TYPE.
var (
	RED_DOT_MAIL_SUB_TYPE_name = map[int32]string{
		0: "RDMST_DEFAULT",
		1: "RDMST_NOTIFY",
		2: "RDMST_MAIL",
	}
	RED_DOT_MAIL_SUB_TYPE_value = map[string]int32{
		"RDMST_DEFAULT": 0,
		"RDMST_NOTIFY":  1,
		"RDMST_MAIL":    2,
	}
)

func (x RED_DOT_MAIL_SUB_TYPE) Enum() *RED_DOT_MAIL_SUB_TYPE {
	p := new(RED_DOT_MAIL_SUB_TYPE)
	*p = x
	return p
}

func (x RED_DOT_MAIL_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RED_DOT_MAIL_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[22].Descriptor()
}

func (RED_DOT_MAIL_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[22]
}

func (x RED_DOT_MAIL_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RED_DOT_MAIL_SUB_TYPE.Descriptor instead.
func (RED_DOT_MAIL_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{22}
}

// 仓库子模块
type RED_DOT_WAREHOUSE_SUB_TYPE int32

const (
	RED_DOT_WAREHOUSE_SUB_TYPE_RDWST_DEFAULT RED_DOT_WAREHOUSE_SUB_TYPE = 0 // 没有子模块就是默认
)

// Enum value maps for RED_DOT_WAREHOUSE_SUB_TYPE.
var (
	RED_DOT_WAREHOUSE_SUB_TYPE_name = map[int32]string{
		0: "RDWST_DEFAULT",
	}
	RED_DOT_WAREHOUSE_SUB_TYPE_value = map[string]int32{
		"RDWST_DEFAULT": 0,
	}
)

func (x RED_DOT_WAREHOUSE_SUB_TYPE) Enum() *RED_DOT_WAREHOUSE_SUB_TYPE {
	p := new(RED_DOT_WAREHOUSE_SUB_TYPE)
	*p = x
	return p
}

func (x RED_DOT_WAREHOUSE_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RED_DOT_WAREHOUSE_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[23].Descriptor()
}

func (RED_DOT_WAREHOUSE_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[23]
}

func (x RED_DOT_WAREHOUSE_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RED_DOT_WAREHOUSE_SUB_TYPE.Descriptor instead.
func (RED_DOT_WAREHOUSE_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{23}
}

// 成就子模块
type RED_DOT_ACHIEVEMENT_SUB_TYPE int32

const (
	RED_DOT_ACHIEVEMENT_SUB_TYPE_RDAST_DEFAULT RED_DOT_ACHIEVEMENT_SUB_TYPE = 0 // 没有子模块就是默认
)

// Enum value maps for RED_DOT_ACHIEVEMENT_SUB_TYPE.
var (
	RED_DOT_ACHIEVEMENT_SUB_TYPE_name = map[int32]string{
		0: "RDAST_DEFAULT",
	}
	RED_DOT_ACHIEVEMENT_SUB_TYPE_value = map[string]int32{
		"RDAST_DEFAULT": 0,
	}
)

func (x RED_DOT_ACHIEVEMENT_SUB_TYPE) Enum() *RED_DOT_ACHIEVEMENT_SUB_TYPE {
	p := new(RED_DOT_ACHIEVEMENT_SUB_TYPE)
	*p = x
	return p
}

func (x RED_DOT_ACHIEVEMENT_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RED_DOT_ACHIEVEMENT_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[24].Descriptor()
}

func (RED_DOT_ACHIEVEMENT_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[24]
}

func (x RED_DOT_ACHIEVEMENT_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RED_DOT_ACHIEVEMENT_SUB_TYPE.Descriptor instead.
func (RED_DOT_ACHIEVEMENT_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{24}
}

// 游戏类型
type GAME_TYPE int32

const (
	GAME_TYPE_GT_UNKNOWN    GAME_TYPE = 0
	GAME_TYPE_GT_RECREATION GAME_TYPE = 1 // 娱乐玩法
	GAME_TYPE_GT_PRIVATE    GAME_TYPE = 2 // 约局玩法
)

// Enum value maps for GAME_TYPE.
var (
	GAME_TYPE_name = map[int32]string{
		0: "GT_UNKNOWN",
		1: "GT_RECREATION",
		2: "GT_PRIVATE",
	}
	GAME_TYPE_value = map[string]int32{
		"GT_UNKNOWN":    0,
		"GT_RECREATION": 1,
		"GT_PRIVATE":    2,
	}
)

func (x GAME_TYPE) Enum() *GAME_TYPE {
	p := new(GAME_TYPE)
	*p = x
	return p
}

func (x GAME_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GAME_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[25].Descriptor()
}

func (GAME_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[25]
}

func (x GAME_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GAME_TYPE.Descriptor instead.
func (GAME_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{25}
}

// 房间类型
type ROOM_TYPE int32

const (
	ROOM_TYPE_RT_UNKNOWN   ROOM_TYPE = 0
	ROOM_TYPE_RT_CASUAL    ROOM_TYPE = 1 // 休闲场
	ROOM_TYPE_RT_PRIVATE   ROOM_TYPE = 2 // 私人约鱼场
	ROOM_TYPE_RT_EXCLUSIVE ROOM_TYPE = 3 // 专属独家场
)

// Enum value maps for ROOM_TYPE.
var (
	ROOM_TYPE_name = map[int32]string{
		0: "RT_UNKNOWN",
		1: "RT_CASUAL",
		2: "RT_PRIVATE",
		3: "RT_EXCLUSIVE",
	}
	ROOM_TYPE_value = map[string]int32{
		"RT_UNKNOWN":   0,
		"RT_CASUAL":    1,
		"RT_PRIVATE":   2,
		"RT_EXCLUSIVE": 3,
	}
)

func (x ROOM_TYPE) Enum() *ROOM_TYPE {
	p := new(ROOM_TYPE)
	*p = x
	return p
}

func (x ROOM_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROOM_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[26].Descriptor()
}

func (ROOM_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[26]
}

func (x ROOM_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROOM_TYPE.Descriptor instead.
func (ROOM_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{26}
}

// 竿操作类型
type ROD_ACTION int32

const (
	ROD_ACTION_RA_UNKNOWN ROD_ACTION = 0
	ROD_ACTION_RA_PUT     ROD_ACTION = 1 // 放线
	ROD_ACTION_RA_TAKE    ROD_ACTION = 2 // 收线
)

// Enum value maps for ROD_ACTION.
var (
	ROD_ACTION_name = map[int32]string{
		0: "RA_UNKNOWN",
		1: "RA_PUT",
		2: "RA_TAKE",
	}
	ROD_ACTION_value = map[string]int32{
		"RA_UNKNOWN": 0,
		"RA_PUT":     1,
		"RA_TAKE":    2,
	}
)

func (x ROD_ACTION) Enum() *ROD_ACTION {
	p := new(ROD_ACTION)
	*p = x
	return p
}

func (x ROD_ACTION) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROD_ACTION) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[27].Descriptor()
}

func (ROD_ACTION) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[27]
}

func (x ROD_ACTION) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROD_ACTION.Descriptor instead.
func (ROD_ACTION) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{27}
}

// 竿操作状态
type ROD_STATUS int32

const (
	ROD_STATUS_RS_UNKNOWN ROD_STATUS = 0
	ROD_STATUS_RS_BEGIN   ROD_STATUS = 1 // 开始
	ROD_STATUS_RS_END     ROD_STATUS = 2 // 结束
)

// Enum value maps for ROD_STATUS.
var (
	ROD_STATUS_name = map[int32]string{
		0: "RS_UNKNOWN",
		1: "RS_BEGIN",
		2: "RS_END",
	}
	ROD_STATUS_value = map[string]int32{
		"RS_UNKNOWN": 0,
		"RS_BEGIN":   1,
		"RS_END":     2,
	}
)

func (x ROD_STATUS) Enum() *ROD_STATUS {
	p := new(ROD_STATUS)
	*p = x
	return p
}

func (x ROD_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROD_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[28].Descriptor()
}

func (ROD_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[28]
}

func (x ROD_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROD_STATUS.Descriptor instead.
func (ROD_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{28}
}

// 上鱼结果
type FISH_RESULT int32

const (
	FISH_RESULT_FR_UNKNOWN    FISH_RESULT = 0 // 未知
	FISH_RESULT_FR_TAKED      FISH_RESULT = 1 // 钓到
	FISH_RESULT_FR_NOTHING    FISH_RESULT = 2 // 空钩
	FISH_RESULT_FR_FISH_OUT   FISH_RESULT = 3 // 鱼跑
	FISH_RESULT_FR_HOOKED     FISH_RESULT = 4 // 咬钩
	FISH_RESULT_FR_FORCE_KEEP FISH_RESULT = 5 //强制保留，不允许脱钩
)

// Enum value maps for FISH_RESULT.
var (
	FISH_RESULT_name = map[int32]string{
		0: "FR_UNKNOWN",
		1: "FR_TAKED",
		2: "FR_NOTHING",
		3: "FR_FISH_OUT",
		4: "FR_HOOKED",
		5: "FR_FORCE_KEEP",
	}
	FISH_RESULT_value = map[string]int32{
		"FR_UNKNOWN":    0,
		"FR_TAKED":      1,
		"FR_NOTHING":    2,
		"FR_FISH_OUT":   3,
		"FR_HOOKED":     4,
		"FR_FORCE_KEEP": 5,
	}
)

func (x FISH_RESULT) Enum() *FISH_RESULT {
	p := new(FISH_RESULT)
	*p = x
	return p
}

func (x FISH_RESULT) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_RESULT) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[29].Descriptor()
}

func (FISH_RESULT) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[29]
}

func (x FISH_RESULT) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_RESULT.Descriptor instead.
func (FISH_RESULT) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{29}
}

// 钓鱼状态
type FISH_STATUS int32

const (
	FISH_STATUS_FS_UNKNOWN FISH_STATUS = 0
	FISH_STATUS_FS_FISHING FISH_STATUS = 1 // 钓鱼中
	FISH_STATUS_FS_FISH_UP FISH_STATUS = 2 // 上鱼
	FISH_STATUS_FS_FISH_IN FISH_STATUS = 3 // 中鱼
	FISH_STATUS_FS_THROW   FISH_STATUS = 4 // 抛竿
)

// Enum value maps for FISH_STATUS.
var (
	FISH_STATUS_name = map[int32]string{
		0: "FS_UNKNOWN",
		1: "FS_FISHING",
		2: "FS_FISH_UP",
		3: "FS_FISH_IN",
		4: "FS_THROW",
	}
	FISH_STATUS_value = map[string]int32{
		"FS_UNKNOWN": 0,
		"FS_FISHING": 1,
		"FS_FISH_UP": 2,
		"FS_FISH_IN": 3,
		"FS_THROW":   4,
	}
)

func (x FISH_STATUS) Enum() *FISH_STATUS {
	p := new(FISH_STATUS)
	*p = x
	return p
}

func (x FISH_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[30].Descriptor()
}

func (FISH_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[30]
}

func (x FISH_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_STATUS.Descriptor instead.
func (FISH_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{30}
}

// 天气类型
type WEATHER_TYPE int32

const (
	WEATHER_TYPE_WT_UNKNOWN       WEATHER_TYPE = 0 // 未知
	WEATHER_TYPE_WT_SUNNY         WEATHER_TYPE = 1 // 晴天
	WEATHER_TYPE_WT_CLOUDY        WEATHER_TYPE = 2 // 多云
	WEATHER_TYPE_WT_OVERCAST      WEATHER_TYPE = 3 // 阴天
	WEATHER_TYPE_WT_FINE_RAIN     WEATHER_TYPE = 4 // 小雨
	WEATHER_TYPE_WT_MODERATE_RAIN WEATHER_TYPE = 5 // 中雨
	WEATHER_TYPE_WT_HEAVY_RAIN    WEATHER_TYPE = 6 // 大雨
	WEATHER_TYPE_WT_STORMY        WEATHER_TYPE = 7 // 暴雨
	WEATHER_TYPE_WT_THUNDERSTORM  WEATHER_TYPE = 8 // 雷阵雨
)

// Enum value maps for WEATHER_TYPE.
var (
	WEATHER_TYPE_name = map[int32]string{
		0: "WT_UNKNOWN",
		1: "WT_SUNNY",
		2: "WT_CLOUDY",
		3: "WT_OVERCAST",
		4: "WT_FINE_RAIN",
		5: "WT_MODERATE_RAIN",
		6: "WT_HEAVY_RAIN",
		7: "WT_STORMY",
		8: "WT_THUNDERSTORM",
	}
	WEATHER_TYPE_value = map[string]int32{
		"WT_UNKNOWN":       0,
		"WT_SUNNY":         1,
		"WT_CLOUDY":        2,
		"WT_OVERCAST":      3,
		"WT_FINE_RAIN":     4,
		"WT_MODERATE_RAIN": 5,
		"WT_HEAVY_RAIN":    6,
		"WT_STORMY":        7,
		"WT_THUNDERSTORM":  8,
	}
)

func (x WEATHER_TYPE) Enum() *WEATHER_TYPE {
	p := new(WEATHER_TYPE)
	*p = x
	return p
}

func (x WEATHER_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WEATHER_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[31].Descriptor()
}

func (WEATHER_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[31]
}

func (x WEATHER_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WEATHER_TYPE.Descriptor instead.
func (WEATHER_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{31}
}

// 水域类型
type WATER_AREA_TYPE int32

const (
	WATER_AREA_TYPE_WAT_UNKNOWN      WATER_AREA_TYPE = 0 // 未知
	WATER_AREA_TYPE_WAT_SLOW_CURRENT WATER_AREA_TYPE = 1 // 缓流
)

// Enum value maps for WATER_AREA_TYPE.
var (
	WATER_AREA_TYPE_name = map[int32]string{
		0: "WAT_UNKNOWN",
		1: "WAT_SLOW_CURRENT",
	}
	WATER_AREA_TYPE_value = map[string]int32{
		"WAT_UNKNOWN":      0,
		"WAT_SLOW_CURRENT": 1,
	}
)

func (x WATER_AREA_TYPE) Enum() *WATER_AREA_TYPE {
	p := new(WATER_AREA_TYPE)
	*p = x
	return p
}

func (x WATER_AREA_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WATER_AREA_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[32].Descriptor()
}

func (WATER_AREA_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[32]
}

func (x WATER_AREA_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WATER_AREA_TYPE.Descriptor instead.
func (WATER_AREA_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{32}
}

// 障碍物类型
type OBSTACLE_TYPE int32

const (
	OBSTACLE_TYPE_OT_NONE       OBSTACLE_TYPE = 0 // 无障碍
	OBSTACLE_TYPE_OT_WEED       OBSTACLE_TYPE = 1 // 杂草
	OBSTACLE_TYPE_OT_WATER_WEED OBSTACLE_TYPE = 2 // 水草
	OBSTACLE_TYPE_OT_PEBBLE     OBSTACLE_TYPE = 3 // 鹅卵石
	OBSTACLE_TYPE_OT_FLOATING   OBSTACLE_TYPE = 4 // 浮萍
	OBSTACLE_TYPE_OT_WOOD       OBSTACLE_TYPE = 5 // 沉木
	OBSTACLE_TYPE_OT_ROCK       OBSTACLE_TYPE = 6 // 石块
)

// Enum value maps for OBSTACLE_TYPE.
var (
	OBSTACLE_TYPE_name = map[int32]string{
		0: "OT_NONE",
		1: "OT_WEED",
		2: "OT_WATER_WEED",
		3: "OT_PEBBLE",
		4: "OT_FLOATING",
		5: "OT_WOOD",
		6: "OT_ROCK",
	}
	OBSTACLE_TYPE_value = map[string]int32{
		"OT_NONE":       0,
		"OT_WEED":       1,
		"OT_WATER_WEED": 2,
		"OT_PEBBLE":     3,
		"OT_FLOATING":   4,
		"OT_WOOD":       5,
		"OT_ROCK":       6,
	}
)

func (x OBSTACLE_TYPE) Enum() *OBSTACLE_TYPE {
	p := new(OBSTACLE_TYPE)
	*p = x
	return p
}

func (x OBSTACLE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OBSTACLE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[33].Descriptor()
}

func (OBSTACLE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[33]
}

func (x OBSTACLE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OBSTACLE_TYPE.Descriptor instead.
func (OBSTACLE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{33}
}

type DIRECTION_TYPE int32

const (
	DIRECTION_TYPE_D_UNKNOWN    DIRECTION_TYPE = 0
	DIRECTION_TYPE_D_NORTH      DIRECTION_TYPE = 1 // 北
	DIRECTION_TYPE_D_NORTH_EAST DIRECTION_TYPE = 2 // 东北
	DIRECTION_TYPE_D_EAST       DIRECTION_TYPE = 3 // 东
	DIRECTION_TYPE_D_SOUTH_EAST DIRECTION_TYPE = 4 // 南东
	DIRECTION_TYPE_D_SOUTH      DIRECTION_TYPE = 5 // 南
	DIRECTION_TYPE_D_SOUTH_WEST DIRECTION_TYPE = 6 // 南西
	DIRECTION_TYPE_D_WEST       DIRECTION_TYPE = 7 // 西
	DIRECTION_TYPE_D_NORTH_WEST DIRECTION_TYPE = 8 // 北西
)

// Enum value maps for DIRECTION_TYPE.
var (
	DIRECTION_TYPE_name = map[int32]string{
		0: "D_UNKNOWN",
		1: "D_NORTH",
		2: "D_NORTH_EAST",
		3: "D_EAST",
		4: "D_SOUTH_EAST",
		5: "D_SOUTH",
		6: "D_SOUTH_WEST",
		7: "D_WEST",
		8: "D_NORTH_WEST",
	}
	DIRECTION_TYPE_value = map[string]int32{
		"D_UNKNOWN":    0,
		"D_NORTH":      1,
		"D_NORTH_EAST": 2,
		"D_EAST":       3,
		"D_SOUTH_EAST": 4,
		"D_SOUTH":      5,
		"D_SOUTH_WEST": 6,
		"D_WEST":       7,
		"D_NORTH_WEST": 8,
	}
)

func (x DIRECTION_TYPE) Enum() *DIRECTION_TYPE {
	p := new(DIRECTION_TYPE)
	*p = x
	return p
}

func (x DIRECTION_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DIRECTION_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[34].Descriptor()
}

func (DIRECTION_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[34]
}

func (x DIRECTION_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DIRECTION_TYPE.Descriptor instead.
func (DIRECTION_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{34}
}

// 鱼入护操作类型
type FISH_ENTRY_OPT_TYPE int32

const (
	FISH_ENTRY_OPT_TYPE_FEOT_UNKNOWN FISH_ENTRY_OPT_TYPE = 0 // 未知
	FISH_ENTRY_OPT_TYPE_FEOT_KEEP    FISH_ENTRY_OPT_TYPE = 1 // 放入
	FISH_ENTRY_OPT_TYPE_FEOT_RELEASE FISH_ENTRY_OPT_TYPE = 2 // 放生
)

// Enum value maps for FISH_ENTRY_OPT_TYPE.
var (
	FISH_ENTRY_OPT_TYPE_name = map[int32]string{
		0: "FEOT_UNKNOWN",
		1: "FEOT_KEEP",
		2: "FEOT_RELEASE",
	}
	FISH_ENTRY_OPT_TYPE_value = map[string]int32{
		"FEOT_UNKNOWN": 0,
		"FEOT_KEEP":    1,
		"FEOT_RELEASE": 2,
	}
)

func (x FISH_ENTRY_OPT_TYPE) Enum() *FISH_ENTRY_OPT_TYPE {
	p := new(FISH_ENTRY_OPT_TYPE)
	*p = x
	return p
}

func (x FISH_ENTRY_OPT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_ENTRY_OPT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[35].Descriptor()
}

func (FISH_ENTRY_OPT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[35]
}

func (x FISH_ENTRY_OPT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_ENTRY_OPT_TYPE.Descriptor instead.
func (FISH_ENTRY_OPT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{35}
}

// 鱼护操作类型
type FISH_KEEPNET_OPT_TYPE int32

const (
	FISH_KEEPNET_OPT_TYPE_FKOT_UNKNOWN FISH_KEEPNET_OPT_TYPE = 0 // 未知
	FISH_KEEPNET_OPT_TYPE_FKOT_DISCARD FISH_KEEPNET_OPT_TYPE = 1 // 丢弃
	FISH_KEEPNET_OPT_TYPE_FKOT_FREEZE  FISH_KEEPNET_OPT_TYPE = 2 // 冷冻
)

// Enum value maps for FISH_KEEPNET_OPT_TYPE.
var (
	FISH_KEEPNET_OPT_TYPE_name = map[int32]string{
		0: "FKOT_UNKNOWN",
		1: "FKOT_DISCARD",
		2: "FKOT_FREEZE",
	}
	FISH_KEEPNET_OPT_TYPE_value = map[string]int32{
		"FKOT_UNKNOWN": 0,
		"FKOT_DISCARD": 1,
		"FKOT_FREEZE":  2,
	}
)

func (x FISH_KEEPNET_OPT_TYPE) Enum() *FISH_KEEPNET_OPT_TYPE {
	p := new(FISH_KEEPNET_OPT_TYPE)
	*p = x
	return p
}

func (x FISH_KEEPNET_OPT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_KEEPNET_OPT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[36].Descriptor()
}

func (FISH_KEEPNET_OPT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[36]
}

func (x FISH_KEEPNET_OPT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_KEEPNET_OPT_TYPE.Descriptor instead.
func (FISH_KEEPNET_OPT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{36}
}

// 鱼属科类型
type FISH_SPECIES_TYPE int32

const (
	FISH_SPECIES_TYPE_FST_UNKNOWN       FISH_SPECIES_TYPE = 0  // 未知
	FISH_SPECIES_TYPE_FST_BASS          FISH_SPECIES_TYPE = 1  // 鲈鱼
	FISH_SPECIES_TYPE_FST_CYPRINIDAE    FISH_SPECIES_TYPE = 2  // 鲤科
	FISH_SPECIES_TYPE_FST_CENTRARCHIDAE FISH_SPECIES_TYPE = 3  // 太阳鱼科
	FISH_SPECIES_TYPE_FST_CATOSTOMIDAE  FISH_SPECIES_TYPE = 4  // 胭脂鱼科
	FISH_SPECIES_TYPE_FST_AMIIDAE       FISH_SPECIES_TYPE = 5  // 弓鳍鱼科
	FISH_SPECIES_TYPE_FST_PERCIDAE      FISH_SPECIES_TYPE = 6  // 鲈科
	FISH_SPECIES_TYPE_FST_ICTALURIDAE   FISH_SPECIES_TYPE = 7  // 真鲇科
	FISH_SPECIES_TYPE_FST_ESOCIDAE      FISH_SPECIES_TYPE = 8  // 狗鱼鱼科
	FISH_SPECIES_TYPE_FST_CLUPEIDAE     FISH_SPECIES_TYPE = 9  // 鲱科
	FISH_SPECIES_TYPE_FST_MORONIDAE     FISH_SPECIES_TYPE = 10 // 狼鲈科
	FISH_SPECIES_TYPE_FST_LEUCISCIDAE   FISH_SPECIES_TYPE = 11 // 雅罗鱼科
	FISH_SPECIES_TYPE_FST_SALMONIDAE    FISH_SPECIES_TYPE = 12 // 鲑科
	FISH_SPECIES_TYPE_FST_TINCIDAE      FISH_SPECIES_TYPE = 13 // 丁桂鱼科
	FISH_SPECIES_TYPE_FST_SCIAENIDAE    FISH_SPECIES_TYPE = 14 // 石首鱼科
	FISH_SPECIES_TYPE_FST_ANGUILLIDAE   FISH_SPECIES_TYPE = 15 // 鳗鲡科
)

// Enum value maps for FISH_SPECIES_TYPE.
var (
	FISH_SPECIES_TYPE_name = map[int32]string{
		0:  "FST_UNKNOWN",
		1:  "FST_BASS",
		2:  "FST_CYPRINIDAE",
		3:  "FST_CENTRARCHIDAE",
		4:  "FST_CATOSTOMIDAE",
		5:  "FST_AMIIDAE",
		6:  "FST_PERCIDAE",
		7:  "FST_ICTALURIDAE",
		8:  "FST_ESOCIDAE",
		9:  "FST_CLUPEIDAE",
		10: "FST_MORONIDAE",
		11: "FST_LEUCISCIDAE",
		12: "FST_SALMONIDAE",
		13: "FST_TINCIDAE",
		14: "FST_SCIAENIDAE",
		15: "FST_ANGUILLIDAE",
	}
	FISH_SPECIES_TYPE_value = map[string]int32{
		"FST_UNKNOWN":       0,
		"FST_BASS":          1,
		"FST_CYPRINIDAE":    2,
		"FST_CENTRARCHIDAE": 3,
		"FST_CATOSTOMIDAE":  4,
		"FST_AMIIDAE":       5,
		"FST_PERCIDAE":      6,
		"FST_ICTALURIDAE":   7,
		"FST_ESOCIDAE":      8,
		"FST_CLUPEIDAE":     9,
		"FST_MORONIDAE":     10,
		"FST_LEUCISCIDAE":   11,
		"FST_SALMONIDAE":    12,
		"FST_TINCIDAE":      13,
		"FST_SCIAENIDAE":    14,
		"FST_ANGUILLIDAE":   15,
	}
)

func (x FISH_SPECIES_TYPE) Enum() *FISH_SPECIES_TYPE {
	p := new(FISH_SPECIES_TYPE)
	*p = x
	return p
}

func (x FISH_SPECIES_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_SPECIES_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[37].Descriptor()
}

func (FISH_SPECIES_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[37]
}

func (x FISH_SPECIES_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_SPECIES_TYPE.Descriptor instead.
func (FISH_SPECIES_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{37}
}

// 钓鱼事件类型
type FISHING_EVENT_TYPE int32

const (
	FISHING_EVENT_TYPE_FET_UNKNOWN         FISHING_EVENT_TYPE = 0 // 未知
	FISHING_EVENT_TYPE_FET_HOOK_FISH       FISHING_EVENT_TYPE = 1 // 中鱼事件
	FISHING_EVENT_TYPE_FET_RELEASE_KEPPENT FISHING_EVENT_TYPE = 2 // 丢弃事件
	FISHING_EVENT_TYPE_FET_CONGRATULATION  FISHING_EVENT_TYPE = 3 // 祝贺事件
)

// Enum value maps for FISHING_EVENT_TYPE.
var (
	FISHING_EVENT_TYPE_name = map[int32]string{
		0: "FET_UNKNOWN",
		1: "FET_HOOK_FISH",
		2: "FET_RELEASE_KEPPENT",
		3: "FET_CONGRATULATION",
	}
	FISHING_EVENT_TYPE_value = map[string]int32{
		"FET_UNKNOWN":         0,
		"FET_HOOK_FISH":       1,
		"FET_RELEASE_KEPPENT": 2,
		"FET_CONGRATULATION":  3,
	}
)

func (x FISHING_EVENT_TYPE) Enum() *FISHING_EVENT_TYPE {
	p := new(FISHING_EVENT_TYPE)
	*p = x
	return p
}

func (x FISHING_EVENT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_EVENT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[38].Descriptor()
}

func (FISHING_EVENT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[38]
}

func (x FISHING_EVENT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_EVENT_TYPE.Descriptor instead.
func (FISHING_EVENT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{38}
}

// 中鱼计算类型
type HOOK_FISH_CALC_TYPE int32

const (
	HOOK_FISH_CALC_TYPE_HFCT_UNKNOWN HOOK_FISH_CALC_TYPE = 0 // 未知
	HOOK_FISH_CALC_TYPE_HFCT_START   HOOK_FISH_CALC_TYPE = 1 // 开始计算
	HOOK_FISH_CALC_TYPE_HFCT_RESET   HOOK_FISH_CALC_TYPE = 2 // 重置计算
)

// Enum value maps for HOOK_FISH_CALC_TYPE.
var (
	HOOK_FISH_CALC_TYPE_name = map[int32]string{
		0: "HFCT_UNKNOWN",
		1: "HFCT_START",
		2: "HFCT_RESET",
	}
	HOOK_FISH_CALC_TYPE_value = map[string]int32{
		"HFCT_UNKNOWN": 0,
		"HFCT_START":   1,
		"HFCT_RESET":   2,
	}
)

func (x HOOK_FISH_CALC_TYPE) Enum() *HOOK_FISH_CALC_TYPE {
	p := new(HOOK_FISH_CALC_TYPE)
	*p = x
	return p
}

func (x HOOK_FISH_CALC_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HOOK_FISH_CALC_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[39].Descriptor()
}

func (HOOK_FISH_CALC_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[39]
}

func (x HOOK_FISH_CALC_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HOOK_FISH_CALC_TYPE.Descriptor instead.
func (HOOK_FISH_CALC_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{39}
}

// 类别
type ITEM_CATEGORY int32

const (
	ITEM_CATEGORY_IC_UNKNOWN   ITEM_CATEGORY = 0
	ITEM_CATEGORY_IC_CURRENCY  ITEM_CATEGORY = 1  // 货币
	ITEM_CATEGORY_IC_PROP      ITEM_CATEGORY = 2  // 道具
	ITEM_CATEGORY_IC_TACKLE    ITEM_CATEGORY = 3  // 渔具
	ITEM_CATEGORY_IC_WEARABLE  ITEM_CATEGORY = 4  // 时装
	ITEM_CATEGORY_IC_EQUIP     ITEM_CATEGORY = 5  // 装备，武器，工具类
	ITEM_CATEGORY_IC_TICKET    ITEM_CATEGORY = 6  // 票券
	ITEM_CATEGORY_IC_FRAGMENTS ITEM_CATEGORY = 7  // 合成系统碎片
	ITEM_CATEGORY_IC_REWARD    ITEM_CATEGORY = 8  // 奖励物品
	ITEM_CATEGORY_IC_GOODS     ITEM_CATEGORY = 9  // 商品
	ITEM_CATEGORY_IC_CATCH     ITEM_CATEGORY = 10 // 捕获物
)

// Enum value maps for ITEM_CATEGORY.
var (
	ITEM_CATEGORY_name = map[int32]string{
		0:  "IC_UNKNOWN",
		1:  "IC_CURRENCY",
		2:  "IC_PROP",
		3:  "IC_TACKLE",
		4:  "IC_WEARABLE",
		5:  "IC_EQUIP",
		6:  "IC_TICKET",
		7:  "IC_FRAGMENTS",
		8:  "IC_REWARD",
		9:  "IC_GOODS",
		10: "IC_CATCH",
	}
	ITEM_CATEGORY_value = map[string]int32{
		"IC_UNKNOWN":   0,
		"IC_CURRENCY":  1,
		"IC_PROP":      2,
		"IC_TACKLE":    3,
		"IC_WEARABLE":  4,
		"IC_EQUIP":     5,
		"IC_TICKET":    6,
		"IC_FRAGMENTS": 7,
		"IC_REWARD":    8,
		"IC_GOODS":     9,
		"IC_CATCH":     10,
	}
)

func (x ITEM_CATEGORY) Enum() *ITEM_CATEGORY {
	p := new(ITEM_CATEGORY)
	*p = x
	return p
}

func (x ITEM_CATEGORY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_CATEGORY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[40].Descriptor()
}

func (ITEM_CATEGORY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[40]
}

func (x ITEM_CATEGORY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_CATEGORY.Descriptor instead.
func (ITEM_CATEGORY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{40}
}

// 道具类型
type ITEM_TYPE int32

const (
	ITEM_TYPE_IT_UNKNOWN             ITEM_TYPE = 0
	ITEM_TYPE_IT_CURRENCY_COIN       ITEM_TYPE = 101  // 金币
	ITEM_TYPE_IT_CURRENCY_DIAMOND    ITEM_TYPE = 102  // 钻石
	ITEM_TYPE_IT_CURRENCY_ENERGY     ITEM_TYPE = 103  // 体力
	ITEM_TYPE_IT_CURRENCY_EXP        ITEM_TYPE = 104  // 经验
	ITEM_TYPE_IT_PROP_PROBE          ITEM_TYPE = 201  // 探测器
	ITEM_TYPE_IT_PROP_FOOD           ITEM_TYPE = 202  // 食物
	ITEM_TYPE_IT_TACKLE_RODS         ITEM_TYPE = 301  // 钓竿
	ITEM_TYPE_IT_TACKLE_REEl         ITEM_TYPE = 302  // 渔轮
	ITEM_TYPE_IT_TACKLE_LINE         ITEM_TYPE = 303  // 主线
	ITEM_TYPE_IT_TACKLE_LEADER       ITEM_TYPE = 304  // 子线
	ITEM_TYPE_IT_TACKLE_BAIT         ITEM_TYPE = 305  // 活饵
	ITEM_TYPE_IT_TACKLE_BOBBERS      ITEM_TYPE = 306  // 浮漂
	ITEM_TYPE_IT_TACKLE_KEEPNETS     ITEM_TYPE = 307  // 鱼库
	ITEM_TYPE_IT_TACKLE_HOOKS        ITEM_TYPE = 308  // 鱼钩
	ITEM_TYPE_IT_TACKLE_LURES        ITEM_TYPE = 309  // 拟饵
	ITEM_TYPE_IT_WEARABLE_HEAD_FRAME ITEM_TYPE = 401  // 头像框
	ITEM_TYPE_IT_WEARABLE_AVATAR     ITEM_TYPE = 402  // 头像
	ITEM_TYPE_IT_EQUIP_SINKERS       ITEM_TYPE = 501  // 铅坠
	ITEM_TYPE_IT_EQUIP_HOOK_REMOVERS ITEM_TYPE = 502  // 脱勾器
	ITEM_TYPE_IT_TICKET_MATCH        ITEM_TYPE = 601  // 比赛券
	ITEM_TYPE_IT_FRAGMENTS_CARD      ITEM_TYPE = 701  // 卡牌
	ITEM_TYPE_IT_REWARD_BAG          ITEM_TYPE = 801  // 礼包奖励
	ITEM_TYPE_IT_GOODS_CHARGE        ITEM_TYPE = 901  // 支付商品
	ITEM_TYPE_IT_CATCH_FISH          ITEM_TYPE = 1001 // 鱼获
)

// Enum value maps for ITEM_TYPE.
var (
	ITEM_TYPE_name = map[int32]string{
		0:    "IT_UNKNOWN",
		101:  "IT_CURRENCY_COIN",
		102:  "IT_CURRENCY_DIAMOND",
		103:  "IT_CURRENCY_ENERGY",
		104:  "IT_CURRENCY_EXP",
		201:  "IT_PROP_PROBE",
		202:  "IT_PROP_FOOD",
		301:  "IT_TACKLE_RODS",
		302:  "IT_TACKLE_REEl",
		303:  "IT_TACKLE_LINE",
		304:  "IT_TACKLE_LEADER",
		305:  "IT_TACKLE_BAIT",
		306:  "IT_TACKLE_BOBBERS",
		307:  "IT_TACKLE_KEEPNETS",
		308:  "IT_TACKLE_HOOKS",
		309:  "IT_TACKLE_LURES",
		401:  "IT_WEARABLE_HEAD_FRAME",
		402:  "IT_WEARABLE_AVATAR",
		501:  "IT_EQUIP_SINKERS",
		502:  "IT_EQUIP_HOOK_REMOVERS",
		601:  "IT_TICKET_MATCH",
		701:  "IT_FRAGMENTS_CARD",
		801:  "IT_REWARD_BAG",
		901:  "IT_GOODS_CHARGE",
		1001: "IT_CATCH_FISH",
	}
	ITEM_TYPE_value = map[string]int32{
		"IT_UNKNOWN":             0,
		"IT_CURRENCY_COIN":       101,
		"IT_CURRENCY_DIAMOND":    102,
		"IT_CURRENCY_ENERGY":     103,
		"IT_CURRENCY_EXP":        104,
		"IT_PROP_PROBE":          201,
		"IT_PROP_FOOD":           202,
		"IT_TACKLE_RODS":         301,
		"IT_TACKLE_REEl":         302,
		"IT_TACKLE_LINE":         303,
		"IT_TACKLE_LEADER":       304,
		"IT_TACKLE_BAIT":         305,
		"IT_TACKLE_BOBBERS":      306,
		"IT_TACKLE_KEEPNETS":     307,
		"IT_TACKLE_HOOKS":        308,
		"IT_TACKLE_LURES":        309,
		"IT_WEARABLE_HEAD_FRAME": 401,
		"IT_WEARABLE_AVATAR":     402,
		"IT_EQUIP_SINKERS":       501,
		"IT_EQUIP_HOOK_REMOVERS": 502,
		"IT_TICKET_MATCH":        601,
		"IT_FRAGMENTS_CARD":      701,
		"IT_REWARD_BAG":          801,
		"IT_GOODS_CHARGE":        901,
		"IT_CATCH_FISH":          1001,
	}
)

func (x ITEM_TYPE) Enum() *ITEM_TYPE {
	p := new(ITEM_TYPE)
	*p = x
	return p
}

func (x ITEM_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[41].Descriptor()
}

func (ITEM_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[41]
}

func (x ITEM_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_TYPE.Descriptor instead.
func (ITEM_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{41}
}

// 道具额外属性键值
type ITEM_EXTRA_KEY int32

const (
	ITEM_EXTRA_KEY_IEK_UNKNOWN         ITEM_EXTRA_KEY = 0
	ITEM_EXTRA_KEY_IEK_CURR_DURABILITY ITEM_EXTRA_KEY = 1 // 当前耐久度
	ITEM_EXTRA_KEY_IEK_MAX_DURABILITY  ITEM_EXTRA_KEY = 2 // 当前耐久度上限
)

// Enum value maps for ITEM_EXTRA_KEY.
var (
	ITEM_EXTRA_KEY_name = map[int32]string{
		0: "IEK_UNKNOWN",
		1: "IEK_CURR_DURABILITY",
		2: "IEK_MAX_DURABILITY",
	}
	ITEM_EXTRA_KEY_value = map[string]int32{
		"IEK_UNKNOWN":         0,
		"IEK_CURR_DURABILITY": 1,
		"IEK_MAX_DURABILITY":  2,
	}
)

func (x ITEM_EXTRA_KEY) Enum() *ITEM_EXTRA_KEY {
	p := new(ITEM_EXTRA_KEY)
	*p = x
	return p
}

func (x ITEM_EXTRA_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_EXTRA_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[42].Descriptor()
}

func (ITEM_EXTRA_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[42]
}

func (x ITEM_EXTRA_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_EXTRA_KEY.Descriptor instead.
func (ITEM_EXTRA_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{42}
}

// 不堆叠类型 (货币默认堆叠 200以下 其他相同属性进行组合)
type UNSTACK_TYPE int32

const (
	UNSTACK_TYPE_UST_UNKNOWN       UNSTACK_TYPE = 0
	UNSTACK_TYPE_UST_TACKLE_RODS   UNSTACK_TYPE = 301 // 钓竿
	UNSTACK_TYPE_UST_TACKLE_REEl   UNSTACK_TYPE = 302 // 渔轮
	UNSTACK_TYPE_UST_TACKLE_LINE   UNSTACK_TYPE = 303 // 主线
	UNSTACK_TYPE_UST_TACKLE_LEADER UNSTACK_TYPE = 304 // 子线
)

// Enum value maps for UNSTACK_TYPE.
var (
	UNSTACK_TYPE_name = map[int32]string{
		0:   "UST_UNKNOWN",
		301: "UST_TACKLE_RODS",
		302: "UST_TACKLE_REEl",
		303: "UST_TACKLE_LINE",
		304: "UST_TACKLE_LEADER",
	}
	UNSTACK_TYPE_value = map[string]int32{
		"UST_UNKNOWN":       0,
		"UST_TACKLE_RODS":   301,
		"UST_TACKLE_REEl":   302,
		"UST_TACKLE_LINE":   303,
		"UST_TACKLE_LEADER": 304,
	}
)

func (x UNSTACK_TYPE) Enum() *UNSTACK_TYPE {
	p := new(UNSTACK_TYPE)
	*p = x
	return p
}

func (x UNSTACK_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UNSTACK_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[43].Descriptor()
}

func (UNSTACK_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[43]
}

func (x UNSTACK_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UNSTACK_TYPE.Descriptor instead.
func (UNSTACK_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{43}
}

// 存储类型
type STORAGE_TYPE int32

const (
	STORAGE_TYPE_ST_UNKNOWN STORAGE_TYPE = 0
	STORAGE_TYPE_ST_STORE   STORAGE_TYPE = 1 // 仓库
	STORAGE_TYPE_ST_BAG     STORAGE_TYPE = 2 // 背包
)

// Enum value maps for STORAGE_TYPE.
var (
	STORAGE_TYPE_name = map[int32]string{
		0: "ST_UNKNOWN",
		1: "ST_STORE",
		2: "ST_BAG",
	}
	STORAGE_TYPE_value = map[string]int32{
		"ST_UNKNOWN": 0,
		"ST_STORE":   1,
		"ST_BAG":     2,
	}
)

func (x STORAGE_TYPE) Enum() *STORAGE_TYPE {
	p := new(STORAGE_TYPE)
	*p = x
	return p
}

func (x STORAGE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (STORAGE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[44].Descriptor()
}

func (STORAGE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[44]
}

func (x STORAGE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use STORAGE_TYPE.Descriptor instead.
func (STORAGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{44}
}

type ITEM_STATUS int32

const (
	ITEM_STATUS_IS_UNKNOWN ITEM_STATUS = 0
	ITEM_STATUS_IS_NORMAL  ITEM_STATUS = 1 // 正常
	ITEM_STATUS_IS_EXPIRED ITEM_STATUS = 2 // 过期
)

// Enum value maps for ITEM_STATUS.
var (
	ITEM_STATUS_name = map[int32]string{
		0: "IS_UNKNOWN",
		1: "IS_NORMAL",
		2: "IS_EXPIRED",
	}
	ITEM_STATUS_value = map[string]int32{
		"IS_UNKNOWN": 0,
		"IS_NORMAL":  1,
		"IS_EXPIRED": 2,
	}
)

func (x ITEM_STATUS) Enum() *ITEM_STATUS {
	p := new(ITEM_STATUS)
	*p = x
	return p
}

func (x ITEM_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[45].Descriptor()
}

func (ITEM_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[45]
}

func (x ITEM_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_STATUS.Descriptor instead.
func (ITEM_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{45}
}

type ITEM_OPERATION int32

const (
	ITEM_OPERATION_IO_UNKNOWN ITEM_OPERATION = 0
	ITEM_OPERATION_IO_ADD     ITEM_OPERATION = 1 // 增加
	ITEM_OPERATION_IO_REDUCE  ITEM_OPERATION = 2 // 减少
	ITEM_OPERATION_IO_LOCK    ITEM_OPERATION = 3 // 锁定
	ITEM_OPERATION_IO_FREEZE  ITEM_OPERATION = 4 // 冻结
	ITEM_OPERATION_IO_UPDATE  ITEM_OPERATION = 5 // 更新数据 (仅能以InstanceId作为更新参数)
)

// Enum value maps for ITEM_OPERATION.
var (
	ITEM_OPERATION_name = map[int32]string{
		0: "IO_UNKNOWN",
		1: "IO_ADD",
		2: "IO_REDUCE",
		3: "IO_LOCK",
		4: "IO_FREEZE",
		5: "IO_UPDATE",
	}
	ITEM_OPERATION_value = map[string]int32{
		"IO_UNKNOWN": 0,
		"IO_ADD":     1,
		"IO_REDUCE":  2,
		"IO_LOCK":    3,
		"IO_FREEZE":  4,
		"IO_UPDATE":  5,
	}
)

func (x ITEM_OPERATION) Enum() *ITEM_OPERATION {
	p := new(ITEM_OPERATION)
	*p = x
	return p
}

func (x ITEM_OPERATION) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_OPERATION) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[46].Descriptor()
}

func (ITEM_OPERATION) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[46]
}

func (x ITEM_OPERATION) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_OPERATION.Descriptor instead.
func (ITEM_OPERATION) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{46}
}

// 物品来源
type ITEM_SOURCE_TYPE int32

const (
	ITEM_SOURCE_TYPE_IST_UNKNOWN                   ITEM_SOURCE_TYPE = 0
	ITEM_SOURCE_TYPE_IST_PAY                       ITEM_SOURCE_TYPE = 1  // 支付
	ITEM_SOURCE_TYPE_IST_GM                        ITEM_SOURCE_TYPE = 2  // GM操作
	ITEM_SOURCE_TYPE_IST_DAILY_BONUS               ITEM_SOURCE_TYPE = 3  // 每日奖励
	ITEM_SOURCE_TYPE_IST_STORE_BUY                 ITEM_SOURCE_TYPE = 4  // 商店购买
	ITEM_SOURCE_TYPE_IST_GM_OPERATION              ITEM_SOURCE_TYPE = 6  // GM操作
	ITEM_SOURCE_TYPE_IST_ENTRY_POND_FEE            ITEM_SOURCE_TYPE = 7  // 进入钓场费用
	ITEM_SOURCE_TYPE_IST_FISH_CATCH_ADD            ITEM_SOURCE_TYPE = 8  // 捕获鱼奖励
	ITEM_SOURCE_TYPE_IST_POND_USE_ITEM             ITEM_SOURCE_TYPE = 9  // 钓场使用物品
	ITEM_SOURCE_TYPE_IST_TASK_REWARD               ITEM_SOURCE_TYPE = 10 // 任务奖励
	ITEM_SOURCE_TYPE_IST_EXP_LEVEL_UP              ITEM_SOURCE_TYPE = 11 // 升级奖励
	ITEM_SOURCE_TYPE_IST_TASK_POND_PROGRESS_REWARD ITEM_SOURCE_TYPE = 12 // 任务进度奖励
	ITEM_SOURCE_TYPE_IST_PERSON_MAIL_REWARD        ITEM_SOURCE_TYPE = 13 // 个人邮件奖励
	ITEM_SOURCE_TYPE_IST_SYSTEM_MAIL_REWARD        ITEM_SOURCE_TYPE = 14 // 系统邮件奖励
	ITEM_SOURCE_TYPE_IST_MAIL_REWARD               ITEM_SOURCE_TYPE = 15 // 邮件奖励(一键领取)
	ITEM_SOURCE_TYPE_IST_BAG_MOVE                  ITEM_SOURCE_TYPE = 16 // 背包转移
	ITEM_SOURCE_TYPE_IST_FIX_ITEM                  ITEM_SOURCE_TYPE = 17 // 装备维修
	ITEM_SOURCE_TYPE_IST_ITEM_SELL                 ITEM_SOURCE_TYPE = 18 // 道具出售
	ITEM_SOURCE_TYPE_IST_ITEM_DURABILITY           ITEM_SOURCE_TYPE = 19 // 耐久磨
	ITEM_SOURCE_TYPE_IST_GUIDE_REWARD              ITEM_SOURCE_TYPE = 20 // 新手引导奖励
	ITEM_SOURCE_TYPE_IST_ACTIVITY_CONTINUOUS_LOGIN ITEM_SOURCE_TYPE = 21 // 连续登录奖励
	ITEM_SOURCE_TYPE_IST_SPOT_KILL_LINE            ITEM_SOURCE_TYPE = 22 // 切线丢失
	ITEM_SOURCE_TYPE_IST_SPOT_CATCH_FISH           ITEM_SOURCE_TYPE = 23 // 钓场捕获鱼
	ITEM_SOURCE_TYPE_IST_SPOT_CATCH_NOT_FISH       ITEM_SOURCE_TYPE = 24 // 钓场未捕获鱼
	ITEM_SOURCE_TYPE_IST_CDK_REWARD                ITEM_SOURCE_TYPE = 25 // 兑换码奖励
	ITEM_SOURCE_TYPE_IST_ACTIVITY_REWARD           ITEM_SOURCE_TYPE = 26 // 活动奖励
	ITEM_SOURCE_TYPE_IST_SPOT_THROW_ROD            ITEM_SOURCE_TYPE = 27 // 抛竿
	ITEM_SOURCE_TYPE_IST_POND_STORE_BUY            ITEM_SOURCE_TYPE = 28 // 局内商城购买
)

// Enum value maps for ITEM_SOURCE_TYPE.
var (
	ITEM_SOURCE_TYPE_name = map[int32]string{
		0:  "IST_UNKNOWN",
		1:  "IST_PAY",
		2:  "IST_GM",
		3:  "IST_DAILY_BONUS",
		4:  "IST_STORE_BUY",
		6:  "IST_GM_OPERATION",
		7:  "IST_ENTRY_POND_FEE",
		8:  "IST_FISH_CATCH_ADD",
		9:  "IST_POND_USE_ITEM",
		10: "IST_TASK_REWARD",
		11: "IST_EXP_LEVEL_UP",
		12: "IST_TASK_POND_PROGRESS_REWARD",
		13: "IST_PERSON_MAIL_REWARD",
		14: "IST_SYSTEM_MAIL_REWARD",
		15: "IST_MAIL_REWARD",
		16: "IST_BAG_MOVE",
		17: "IST_FIX_ITEM",
		18: "IST_ITEM_SELL",
		19: "IST_ITEM_DURABILITY",
		20: "IST_GUIDE_REWARD",
		21: "IST_ACTIVITY_CONTINUOUS_LOGIN",
		22: "IST_SPOT_KILL_LINE",
		23: "IST_SPOT_CATCH_FISH",
		24: "IST_SPOT_CATCH_NOT_FISH",
		25: "IST_CDK_REWARD",
		26: "IST_ACTIVITY_REWARD",
		27: "IST_SPOT_THROW_ROD",
		28: "IST_POND_STORE_BUY",
	}
	ITEM_SOURCE_TYPE_value = map[string]int32{
		"IST_UNKNOWN":                   0,
		"IST_PAY":                       1,
		"IST_GM":                        2,
		"IST_DAILY_BONUS":               3,
		"IST_STORE_BUY":                 4,
		"IST_GM_OPERATION":              6,
		"IST_ENTRY_POND_FEE":            7,
		"IST_FISH_CATCH_ADD":            8,
		"IST_POND_USE_ITEM":             9,
		"IST_TASK_REWARD":               10,
		"IST_EXP_LEVEL_UP":              11,
		"IST_TASK_POND_PROGRESS_REWARD": 12,
		"IST_PERSON_MAIL_REWARD":        13,
		"IST_SYSTEM_MAIL_REWARD":        14,
		"IST_MAIL_REWARD":               15,
		"IST_BAG_MOVE":                  16,
		"IST_FIX_ITEM":                  17,
		"IST_ITEM_SELL":                 18,
		"IST_ITEM_DURABILITY":           19,
		"IST_GUIDE_REWARD":              20,
		"IST_ACTIVITY_CONTINUOUS_LOGIN": 21,
		"IST_SPOT_KILL_LINE":            22,
		"IST_SPOT_CATCH_FISH":           23,
		"IST_SPOT_CATCH_NOT_FISH":       24,
		"IST_CDK_REWARD":                25,
		"IST_ACTIVITY_REWARD":           26,
		"IST_SPOT_THROW_ROD":            27,
		"IST_POND_STORE_BUY":            28,
	}
)

func (x ITEM_SOURCE_TYPE) Enum() *ITEM_SOURCE_TYPE {
	p := new(ITEM_SOURCE_TYPE)
	*p = x
	return p
}

func (x ITEM_SOURCE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_SOURCE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[47].Descriptor()
}

func (ITEM_SOURCE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[47]
}

func (x ITEM_SOURCE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_SOURCE_TYPE.Descriptor instead.
func (ITEM_SOURCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{47}
}

// 奖励展示类型
type REWARD_SHOW_TYPE int32

const (
	REWARD_SHOW_TYPE_RST_UNKNOWN REWARD_SHOW_TYPE = 0
	REWARD_SHOW_TYPE_RST_NORMAL  REWARD_SHOW_TYPE = 1
	REWARD_SHOW_TYPE_RST_TIP     REWARD_SHOW_TYPE = 2 // 弱Tips提示
	REWARD_SHOW_TYPE_RST_DIALOG  REWARD_SHOW_TYPE = 3 // 强弹窗
	REWARD_SHOW_TYPE_RST_COMBINE REWARD_SHOW_TYPE = 4 // 合并展示
)

// Enum value maps for REWARD_SHOW_TYPE.
var (
	REWARD_SHOW_TYPE_name = map[int32]string{
		0: "RST_UNKNOWN",
		1: "RST_NORMAL",
		2: "RST_TIP",
		3: "RST_DIALOG",
		4: "RST_COMBINE",
	}
	REWARD_SHOW_TYPE_value = map[string]int32{
		"RST_UNKNOWN": 0,
		"RST_NORMAL":  1,
		"RST_TIP":     2,
		"RST_DIALOG":  3,
		"RST_COMBINE": 4,
	}
)

func (x REWARD_SHOW_TYPE) Enum() *REWARD_SHOW_TYPE {
	p := new(REWARD_SHOW_TYPE)
	*p = x
	return p
}

func (x REWARD_SHOW_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (REWARD_SHOW_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[48].Descriptor()
}

func (REWARD_SHOW_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[48]
}

func (x REWARD_SHOW_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use REWARD_SHOW_TYPE.Descriptor instead.
func (REWARD_SHOW_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{48}
}

// 物品使用类型
type BAG_OPERATE_TYPE int32

const (
	BAG_OPERATE_TYPE_BOT_UNKNOWN BAG_OPERATE_TYPE = 0
	BAG_OPERATE_TYPE_BOT_USE     BAG_OPERATE_TYPE = 1 // 使用
	BAG_OPERATE_TYPE_BOT_SALE    BAG_OPERATE_TYPE = 2 // 售卖
	BAG_OPERATE_TYPE_BOT_LOCK    BAG_OPERATE_TYPE = 3 // 锁定
)

// Enum value maps for BAG_OPERATE_TYPE.
var (
	BAG_OPERATE_TYPE_name = map[int32]string{
		0: "BOT_UNKNOWN",
		1: "BOT_USE",
		2: "BOT_SALE",
		3: "BOT_LOCK",
	}
	BAG_OPERATE_TYPE_value = map[string]int32{
		"BOT_UNKNOWN": 0,
		"BOT_USE":     1,
		"BOT_SALE":    2,
		"BOT_LOCK":    3,
	}
)

func (x BAG_OPERATE_TYPE) Enum() *BAG_OPERATE_TYPE {
	p := new(BAG_OPERATE_TYPE)
	*p = x
	return p
}

func (x BAG_OPERATE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BAG_OPERATE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[49].Descriptor()
}

func (BAG_OPERATE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[49]
}

func (x BAG_OPERATE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BAG_OPERATE_TYPE.Descriptor instead.
func (BAG_OPERATE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{49}
}

// 物品品质类型
type ITEM_QUALITY_TYPE int32

const (
	ITEM_QUALITY_TYPE_IQT_UNKNOWN   ITEM_QUALITY_TYPE = 0
	ITEM_QUALITY_TYPE_IQT_NORMAL    ITEM_QUALITY_TYPE = 1 // 普通 C
	ITEM_QUALITY_TYPE_IQT_EPIC      ITEM_QUALITY_TYPE = 2 // 优秀 B
	ITEM_QUALITY_TYPE_IQT_RARE      ITEM_QUALITY_TYPE = 3 // 稀有 A
	ITEM_QUALITY_TYPE_IQT_UNIQUE    ITEM_QUALITY_TYPE = 4 // 特殊 S
	ITEM_QUALITY_TYPE_IQT_SUPERB    ITEM_QUALITY_TYPE = 5 // 极佳 SS
	ITEM_QUALITY_TYPE_IQT_LEGENDARY ITEM_QUALITY_TYPE = 6 // 传说 SSS
)

// Enum value maps for ITEM_QUALITY_TYPE.
var (
	ITEM_QUALITY_TYPE_name = map[int32]string{
		0: "IQT_UNKNOWN",
		1: "IQT_NORMAL",
		2: "IQT_EPIC",
		3: "IQT_RARE",
		4: "IQT_UNIQUE",
		5: "IQT_SUPERB",
		6: "IQT_LEGENDARY",
	}
	ITEM_QUALITY_TYPE_value = map[string]int32{
		"IQT_UNKNOWN":   0,
		"IQT_NORMAL":    1,
		"IQT_EPIC":      2,
		"IQT_RARE":      3,
		"IQT_UNIQUE":    4,
		"IQT_SUPERB":    5,
		"IQT_LEGENDARY": 6,
	}
)

func (x ITEM_QUALITY_TYPE) Enum() *ITEM_QUALITY_TYPE {
	p := new(ITEM_QUALITY_TYPE)
	*p = x
	return p
}

func (x ITEM_QUALITY_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ITEM_QUALITY_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[50].Descriptor()
}

func (ITEM_QUALITY_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[50]
}

func (x ITEM_QUALITY_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ITEM_QUALITY_TYPE.Descriptor instead.
func (ITEM_QUALITY_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{50}
}

// 鱼竿子类型
type ROD_SUB_TYPE int32

const (
	ROD_SUB_TYPE_RODST_UNKNOWN  ROD_SUB_TYPE = 0
	ROD_SUB_TYPE_RODST_FLOAT    ROD_SUB_TYPE = 1 // 浮钓竿
	ROD_SUB_TYPE_RODST_SPINNING ROD_SUB_TYPE = 2 // 直柄竿
	ROD_SUB_TYPE_RODST_CASTING  ROD_SUB_TYPE = 3 // 枪柄竿
	ROD_SUB_TYPE_RODST_DIVIDER  ROD_SUB_TYPE = 4 // 分区竿
	ROD_SUB_TYPE_RODST_EXTEND   ROD_SUB_TYPE = 5 // 伸缩竿
)

// Enum value maps for ROD_SUB_TYPE.
var (
	ROD_SUB_TYPE_name = map[int32]string{
		0: "RODST_UNKNOWN",
		1: "RODST_FLOAT",
		2: "RODST_SPINNING",
		3: "RODST_CASTING",
		4: "RODST_DIVIDER",
		5: "RODST_EXTEND",
	}
	ROD_SUB_TYPE_value = map[string]int32{
		"RODST_UNKNOWN":  0,
		"RODST_FLOAT":    1,
		"RODST_SPINNING": 2,
		"RODST_CASTING":  3,
		"RODST_DIVIDER":  4,
		"RODST_EXTEND":   5,
	}
)

func (x ROD_SUB_TYPE) Enum() *ROD_SUB_TYPE {
	p := new(ROD_SUB_TYPE)
	*p = x
	return p
}

func (x ROD_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROD_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[51].Descriptor()
}

func (ROD_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[51]
}

func (x ROD_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROD_SUB_TYPE.Descriptor instead.
func (ROD_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{51}
}

// 鱼轮子类型
type REEL_SUB_TYPE int32

const (
	REEL_SUB_TYPE_REELST_UNKNOWN REEL_SUB_TYPE = 0
	REEL_SUB_TYPE_REELST_SPIN    REEL_SUB_TYPE = 1 // 纺车轮
	REEL_SUB_TYPE_REELST_CAST    REEL_SUB_TYPE = 2 // 水滴轮
	REEL_SUB_TYPE_REELST_SPOOL   REEL_SUB_TYPE = 3 // 鼓轮
)

// Enum value maps for REEL_SUB_TYPE.
var (
	REEL_SUB_TYPE_name = map[int32]string{
		0: "REELST_UNKNOWN",
		1: "REELST_SPIN",
		2: "REELST_CAST",
		3: "REELST_SPOOL",
	}
	REEL_SUB_TYPE_value = map[string]int32{
		"REELST_UNKNOWN": 0,
		"REELST_SPIN":    1,
		"REELST_CAST":    2,
		"REELST_SPOOL":   3,
	}
)

func (x REEL_SUB_TYPE) Enum() *REEL_SUB_TYPE {
	p := new(REEL_SUB_TYPE)
	*p = x
	return p
}

func (x REEL_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (REEL_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[52].Descriptor()
}

func (REEL_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[52]
}

func (x REEL_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use REEL_SUB_TYPE.Descriptor instead.
func (REEL_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{52}
}

// 主线子类型
type LINE_SUB_TYPE int32

const (
	LINE_SUB_TYPE_LINEST_UNKNOWN      LINE_SUB_TYPE = 0
	LINE_SUB_TYPE_LINEST_NYLON        LINE_SUB_TYPE = 1 // 尼龙线
	LINE_SUB_TYPE_LINEST_CARBON       LINE_SUB_TYPE = 2 // 碳素线
	LINE_SUB_TYPE_LINEST_COMPOSITE    LINE_SUB_TYPE = 3 // 编制线
	LINE_SUB_TYPE_LINEST_FLUOROCARBON LINE_SUB_TYPE = 4 // 氟碳线
)

// Enum value maps for LINE_SUB_TYPE.
var (
	LINE_SUB_TYPE_name = map[int32]string{
		0: "LINEST_UNKNOWN",
		1: "LINEST_NYLON",
		2: "LINEST_CARBON",
		3: "LINEST_COMPOSITE",
		4: "LINEST_FLUOROCARBON",
	}
	LINE_SUB_TYPE_value = map[string]int32{
		"LINEST_UNKNOWN":      0,
		"LINEST_NYLON":        1,
		"LINEST_CARBON":       2,
		"LINEST_COMPOSITE":    3,
		"LINEST_FLUOROCARBON": 4,
	}
)

func (x LINE_SUB_TYPE) Enum() *LINE_SUB_TYPE {
	p := new(LINE_SUB_TYPE)
	*p = x
	return p
}

func (x LINE_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LINE_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[53].Descriptor()
}

func (LINE_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[53]
}

func (x LINE_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LINE_SUB_TYPE.Descriptor instead.
func (LINE_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{53}
}

// 子线子类型
type LEADER_SUB_TYPE int32

const (
	LEADER_SUB_TYPE_LEADERST_UNKNOWN LEADER_SUB_TYPE = 0
	LEADER_SUB_TYPE_LEADERST_FLOAT   LEADER_SUB_TYPE = 1 // 浮钓子线
	LEADER_SUB_TYPE_LEADERST_LURE    LEADER_SUB_TYPE = 2 // 路亚前导线
)

// Enum value maps for LEADER_SUB_TYPE.
var (
	LEADER_SUB_TYPE_name = map[int32]string{
		0: "LEADERST_UNKNOWN",
		1: "LEADERST_FLOAT",
		2: "LEADERST_LURE",
	}
	LEADER_SUB_TYPE_value = map[string]int32{
		"LEADERST_UNKNOWN": 0,
		"LEADERST_FLOAT":   1,
		"LEADERST_LURE":    2,
	}
)

func (x LEADER_SUB_TYPE) Enum() *LEADER_SUB_TYPE {
	p := new(LEADER_SUB_TYPE)
	*p = x
	return p
}

func (x LEADER_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LEADER_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[54].Descriptor()
}

func (LEADER_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[54]
}

func (x LEADER_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LEADER_SUB_TYPE.Descriptor instead.
func (LEADER_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{54}
}

// 真饵子类型
type BAIT_SUB_TYPE int32

const (
	BAIT_SUB_TYPE_BAITST_UNKNOWN      BAIT_SUB_TYPE = 0
	BAIT_SUB_TYPE_BAITST_LIVE         BAIT_SUB_TYPE = 101 // 活饵
	BAIT_SUB_TYPE_BAITST_DEAD         BAIT_SUB_TYPE = 102 // 死饵
	BAIT_SUB_TYPE_BAITST_SEEDS        BAIT_SUB_TYPE = 103 // 种子
	BAIT_SUB_TYPE_BAITST_INSECT       BAIT_SUB_TYPE = 104 // 昆虫
	BAIT_SUB_TYPE_BAITST_INSECT_SHELL BAIT_SUB_TYPE = 105 // 甲壳
	BAIT_SUB_TYPE_BAITST_FISH         BAIT_SUB_TYPE = 106 // 鱼
	BAIT_SUB_TYPE_BAITST_FISH_BLOCK   BAIT_SUB_TYPE = 107 // 鱼块
	BAIT_SUB_TYPE_BAITST_FISH_EGG     BAIT_SUB_TYPE = 108 // 鱼卵
	BAIT_SUB_TYPE_BAITST_FISH_PASTA   BAIT_SUB_TYPE = 109 // 面团
	BAIT_SUB_TYPE_BAITST_GRAIN        BAIT_SUB_TYPE = 110 // [真饵]谷物
	BAIT_SUB_TYPE_BAITST_MEAT         BAIT_SUB_TYPE = 111 // [真饵]肉饵
	BAIT_SUB_TYPE_BAITST_DAIRY        BAIT_SUB_TYPE = 112 // [真饵]乳制
)

// Enum value maps for BAIT_SUB_TYPE.
var (
	BAIT_SUB_TYPE_name = map[int32]string{
		0:   "BAITST_UNKNOWN",
		101: "BAITST_LIVE",
		102: "BAITST_DEAD",
		103: "BAITST_SEEDS",
		104: "BAITST_INSECT",
		105: "BAITST_INSECT_SHELL",
		106: "BAITST_FISH",
		107: "BAITST_FISH_BLOCK",
		108: "BAITST_FISH_EGG",
		109: "BAITST_FISH_PASTA",
		110: "BAITST_GRAIN",
		111: "BAITST_MEAT",
		112: "BAITST_DAIRY",
	}
	BAIT_SUB_TYPE_value = map[string]int32{
		"BAITST_UNKNOWN":      0,
		"BAITST_LIVE":         101,
		"BAITST_DEAD":         102,
		"BAITST_SEEDS":        103,
		"BAITST_INSECT":       104,
		"BAITST_INSECT_SHELL": 105,
		"BAITST_FISH":         106,
		"BAITST_FISH_BLOCK":   107,
		"BAITST_FISH_EGG":     108,
		"BAITST_FISH_PASTA":   109,
		"BAITST_GRAIN":        110,
		"BAITST_MEAT":         111,
		"BAITST_DAIRY":        112,
	}
)

func (x BAIT_SUB_TYPE) Enum() *BAIT_SUB_TYPE {
	p := new(BAIT_SUB_TYPE)
	*p = x
	return p
}

func (x BAIT_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BAIT_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[55].Descriptor()
}

func (BAIT_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[55]
}

func (x BAIT_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BAIT_SUB_TYPE.Descriptor instead.
func (BAIT_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{55}
}

// 拟饵子类型
type LURE_SUB_TYPE int32

const (
	LURE_SUB_TYPE_LUREST_UNKNOWN           LURE_SUB_TYPE = 0   // 无
	LURE_SUB_TYPE_LUREST_SHAD              LURE_SUB_TYPE = 201 // T尾
	LURE_SUB_TYPE_LUREST_GRUB              LURE_SUB_TYPE = 202 // 卷尾
	LURE_SUB_TYPE_LUREST_WORM              LURE_SUB_TYPE = 203 // 软虫
	LURE_SUB_TYPE_LUREST_SHRIMP            LURE_SUB_TYPE = 204 // 虾管
	LURE_SUB_TYPE_LUREST_MINNOW            LURE_SUB_TYPE = 301 // 米诺
	LURE_SUB_TYPE_LUREST_POPPER            LURE_SUB_TYPE = 302 // 波趴
	LURE_SUB_TYPE_LUREST_SPOON             LURE_SUB_TYPE = 303 // 勺子亮片
	LURE_SUB_TYPE_LUREST_SPINNER           LURE_SUB_TYPE = 304 // 旋转亮片
	LURE_SUB_TYPE_LUREST_VIB               LURE_SUB_TYPE = 305 // VIB
	LURE_SUB_TYPE_LUREST_TRACTOR           LURE_SUB_TYPE = 306 // 水面拖拉机
	LURE_SUB_TYPE_LUREST_PENCIL            LURE_SUB_TYPE = 307 // 铅笔
	LURE_SUB_TYPE_LUREST_NOSIE             LURE_SUB_TYPE = 308 // 嘈杂饵
	LURE_SUB_TYPE_LUREST_KNOTTY            LURE_SUB_TYPE = 309 // 多节鱼
	LURE_SUB_TYPE_LUREST_FROG              LURE_SUB_TYPE = 310 // 雷蛙
	LURE_SUB_TYPE_LUREST_BASSJIGGER        LURE_SUB_TYPE = 311 // 胡须佬
	LURE_SUB_TYPE_LUREST_SPINNER_COMPOSITE LURE_SUB_TYPE = 312 // 复合亮片
	LURE_SUB_TYPE_LUREST_SHAKER            LURE_SUB_TYPE = 313 // 摇滚饵
)

// Enum value maps for LURE_SUB_TYPE.
var (
	LURE_SUB_TYPE_name = map[int32]string{
		0:   "LUREST_UNKNOWN",
		201: "LUREST_SHAD",
		202: "LUREST_GRUB",
		203: "LUREST_WORM",
		204: "LUREST_SHRIMP",
		301: "LUREST_MINNOW",
		302: "LUREST_POPPER",
		303: "LUREST_SPOON",
		304: "LUREST_SPINNER",
		305: "LUREST_VIB",
		306: "LUREST_TRACTOR",
		307: "LUREST_PENCIL",
		308: "LUREST_NOSIE",
		309: "LUREST_KNOTTY",
		310: "LUREST_FROG",
		311: "LUREST_BASSJIGGER",
		312: "LUREST_SPINNER_COMPOSITE",
		313: "LUREST_SHAKER",
	}
	LURE_SUB_TYPE_value = map[string]int32{
		"LUREST_UNKNOWN":           0,
		"LUREST_SHAD":              201,
		"LUREST_GRUB":              202,
		"LUREST_WORM":              203,
		"LUREST_SHRIMP":            204,
		"LUREST_MINNOW":            301,
		"LUREST_POPPER":            302,
		"LUREST_SPOON":             303,
		"LUREST_SPINNER":           304,
		"LUREST_VIB":               305,
		"LUREST_TRACTOR":           306,
		"LUREST_PENCIL":            307,
		"LUREST_NOSIE":             308,
		"LUREST_KNOTTY":            309,
		"LUREST_FROG":              310,
		"LUREST_BASSJIGGER":        311,
		"LUREST_SPINNER_COMPOSITE": 312,
		"LUREST_SHAKER":            313,
	}
)

func (x LURE_SUB_TYPE) Enum() *LURE_SUB_TYPE {
	p := new(LURE_SUB_TYPE)
	*p = x
	return p
}

func (x LURE_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LURE_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[56].Descriptor()
}

func (LURE_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[56]
}

func (x LURE_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LURE_SUB_TYPE.Descriptor instead.
func (LURE_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{56}
}

// 浮漂子类型
type BOBBER_SUB_TYPE int32

const (
	BOBBER_SUB_TYPE_BST_UNKNOWN BOBBER_SUB_TYPE = 0
	BOBBER_SUB_TYPE_BST_LIGHT   BOBBER_SUB_TYPE = 1 // 轻型浮漂
	BOBBER_SUB_TYPE_BST_MIDDLE  BOBBER_SUB_TYPE = 2 // 中型浮漂
	BOBBER_SUB_TYPE_BST_HEAVY   BOBBER_SUB_TYPE = 3 // 重型浮漂
	BOBBER_SUB_TYPE_BST_THROUGH BOBBER_SUB_TYPE = 4 // 贯穿漂
	BOBBER_SUB_TYPE_BST_BASE    BOBBER_SUB_TYPE = 5 // 底座漂
)

// Enum value maps for BOBBER_SUB_TYPE.
var (
	BOBBER_SUB_TYPE_name = map[int32]string{
		0: "BST_UNKNOWN",
		1: "BST_LIGHT",
		2: "BST_MIDDLE",
		3: "BST_HEAVY",
		4: "BST_THROUGH",
		5: "BST_BASE",
	}
	BOBBER_SUB_TYPE_value = map[string]int32{
		"BST_UNKNOWN": 0,
		"BST_LIGHT":   1,
		"BST_MIDDLE":  2,
		"BST_HEAVY":   3,
		"BST_THROUGH": 4,
		"BST_BASE":    5,
	}
)

func (x BOBBER_SUB_TYPE) Enum() *BOBBER_SUB_TYPE {
	p := new(BOBBER_SUB_TYPE)
	*p = x
	return p
}

func (x BOBBER_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BOBBER_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[57].Descriptor()
}

func (BOBBER_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[57]
}

func (x BOBBER_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BOBBER_SUB_TYPE.Descriptor instead.
func (BOBBER_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{57}
}

// 鱼钩子类型
type HOOKS_SUB_TYPE int32

const (
	HOOKS_SUB_TYPE_HKST_UNKNOWN    HOOKS_SUB_TYPE = 0
	HOOKS_SUB_TYPE_HKST_LURE       HOOKS_SUB_TYPE = 1   // 路亚钩
	HOOKS_SUB_TYPE_HKST_FLOAT      HOOKS_SUB_TYPE = 2   // 浮钓钩
	HOOKS_SUB_TYPE_HKST_ISENI      HOOKS_SUB_TYPE = 101 // 伊势尼钩
	HOOKS_SUB_TYPE_HKST_SLEEVE     HOOKS_SUB_TYPE = 102 // 袖钩
	HOOKS_SUB_TYPE_HKST_LEAD_HEAD  HOOKS_SUB_TYPE = 201 // 铅头钩
	HOOKS_SUB_TYPE_HKST_FISH_EYE   HOOKS_SUB_TYPE = 202 // 鱼眼钩
	HOOKS_SUB_TYPE_HKST_BENT_SHANK HOOKS_SUB_TYPE = 203 // 曲柄钩
	HOOKS_SUB_TYPE_HKST_WACKY      HOOKS_SUB_TYPE = 204 // wacky钩
)

// Enum value maps for HOOKS_SUB_TYPE.
var (
	HOOKS_SUB_TYPE_name = map[int32]string{
		0:   "HKST_UNKNOWN",
		1:   "HKST_LURE",
		2:   "HKST_FLOAT",
		101: "HKST_ISENI",
		102: "HKST_SLEEVE",
		201: "HKST_LEAD_HEAD",
		202: "HKST_FISH_EYE",
		203: "HKST_BENT_SHANK",
		204: "HKST_WACKY",
	}
	HOOKS_SUB_TYPE_value = map[string]int32{
		"HKST_UNKNOWN":    0,
		"HKST_LURE":       1,
		"HKST_FLOAT":      2,
		"HKST_ISENI":      101,
		"HKST_SLEEVE":     102,
		"HKST_LEAD_HEAD":  201,
		"HKST_FISH_EYE":   202,
		"HKST_BENT_SHANK": 203,
		"HKST_WACKY":      204,
	}
)

func (x HOOKS_SUB_TYPE) Enum() *HOOKS_SUB_TYPE {
	p := new(HOOKS_SUB_TYPE)
	*p = x
	return p
}

func (x HOOKS_SUB_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HOOKS_SUB_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[58].Descriptor()
}

func (HOOKS_SUB_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[58]
}

func (x HOOKS_SUB_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HOOKS_SUB_TYPE.Descriptor instead.
func (HOOKS_SUB_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{58}
}

// 鱼竿调性类型
type ROD_ACTION_TYPE int32

const (
	ROD_ACTION_TYPE_RAT_UNKNOWN ROD_ACTION_TYPE = 0 // 未知
	ROD_ACTION_TYPE_RAT_XS      ROD_ACTION_TYPE = 1 // 超慢
	ROD_ACTION_TYPE_RAT_S       ROD_ACTION_TYPE = 2 // 慢
	ROD_ACTION_TYPE_RAT_MS      ROD_ACTION_TYPE = 3 // 中慢
	ROD_ACTION_TYPE_RAT_M       ROD_ACTION_TYPE = 4 // 中
	ROD_ACTION_TYPE_RAT_MF      ROD_ACTION_TYPE = 5 // 中快
	ROD_ACTION_TYPE_RAT_F       ROD_ACTION_TYPE = 6 // 快
	ROD_ACTION_TYPE_RAT_XF      ROD_ACTION_TYPE = 7 // 超快
)

// Enum value maps for ROD_ACTION_TYPE.
var (
	ROD_ACTION_TYPE_name = map[int32]string{
		0: "RAT_UNKNOWN",
		1: "RAT_XS",
		2: "RAT_S",
		3: "RAT_MS",
		4: "RAT_M",
		5: "RAT_MF",
		6: "RAT_F",
		7: "RAT_XF",
	}
	ROD_ACTION_TYPE_value = map[string]int32{
		"RAT_UNKNOWN": 0,
		"RAT_XS":      1,
		"RAT_S":       2,
		"RAT_MS":      3,
		"RAT_M":       4,
		"RAT_MF":      5,
		"RAT_F":       6,
		"RAT_XF":      7,
	}
)

func (x ROD_ACTION_TYPE) Enum() *ROD_ACTION_TYPE {
	p := new(ROD_ACTION_TYPE)
	*p = x
	return p
}

func (x ROD_ACTION_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROD_ACTION_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[59].Descriptor()
}

func (ROD_ACTION_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[59]
}

func (x ROD_ACTION_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROD_ACTION_TYPE.Descriptor instead.
func (ROD_ACTION_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{59}
}

// 竿硬度类型 分别为软 中软 中 中硬 硬 超硬
type ROD_HARDNESS_TYPE int32

const (
	ROD_HARDNESS_TYPE_RHT_UNKNOWN ROD_HARDNESS_TYPE = 0 // 未知
	ROD_HARDNESS_TYPE_RHT_UL      ROD_HARDNESS_TYPE = 1 // 超软
	ROD_HARDNESS_TYPE_RHT_L       ROD_HARDNESS_TYPE = 2 // 软
	ROD_HARDNESS_TYPE_RHT_ML      ROD_HARDNESS_TYPE = 3 // 中软
	ROD_HARDNESS_TYPE_RHT_M       ROD_HARDNESS_TYPE = 4 // 中
	ROD_HARDNESS_TYPE_RHT_MH      ROD_HARDNESS_TYPE = 5 // 中硬
	ROD_HARDNESS_TYPE_RHT_H       ROD_HARDNESS_TYPE = 6 // 硬
	ROD_HARDNESS_TYPE_RHT_XH      ROD_HARDNESS_TYPE = 7 // 超硬
)

// Enum value maps for ROD_HARDNESS_TYPE.
var (
	ROD_HARDNESS_TYPE_name = map[int32]string{
		0: "RHT_UNKNOWN",
		1: "RHT_UL",
		2: "RHT_L",
		3: "RHT_ML",
		4: "RHT_M",
		5: "RHT_MH",
		6: "RHT_H",
		7: "RHT_XH",
	}
	ROD_HARDNESS_TYPE_value = map[string]int32{
		"RHT_UNKNOWN": 0,
		"RHT_UL":      1,
		"RHT_L":       2,
		"RHT_ML":      3,
		"RHT_M":       4,
		"RHT_MH":      5,
		"RHT_H":       6,
		"RHT_XH":      7,
	}
)

func (x ROD_HARDNESS_TYPE) Enum() *ROD_HARDNESS_TYPE {
	p := new(ROD_HARDNESS_TYPE)
	*p = x
	return p
}

func (x ROD_HARDNESS_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ROD_HARDNESS_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[60].Descriptor()
}

func (ROD_HARDNESS_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[60]
}

func (x ROD_HARDNESS_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ROD_HARDNESS_TYPE.Descriptor instead.
func (ROD_HARDNESS_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{60}
}

// 刺鱼等级
type HOOKSET_RESULT int32

const (
	HOOKSET_RESULT_HSR_UNKNOWN HOOKSET_RESULT = 0 // 无
	HOOKSET_RESULT_HSR_BARELY  HOOKSET_RESULT = 1 // 勉强
	HOOKSET_RESULT_HSR_SUCC    HOOKSET_RESULT = 2 // 完成
	HOOKSET_RESULT_HSR_GOOD    HOOKSET_RESULT = 3 // 优秀
	HOOKSET_RESULT_HSR_PERFECT HOOKSET_RESULT = 4 // 完美
)

// Enum value maps for HOOKSET_RESULT.
var (
	HOOKSET_RESULT_name = map[int32]string{
		0: "HSR_UNKNOWN",
		1: "HSR_BARELY",
		2: "HSR_SUCC",
		3: "HSR_GOOD",
		4: "HSR_PERFECT",
	}
	HOOKSET_RESULT_value = map[string]int32{
		"HSR_UNKNOWN": 0,
		"HSR_BARELY":  1,
		"HSR_SUCC":    2,
		"HSR_GOOD":    3,
		"HSR_PERFECT": 4,
	}
)

func (x HOOKSET_RESULT) Enum() *HOOKSET_RESULT {
	p := new(HOOKSET_RESULT)
	*p = x
	return p
}

func (x HOOKSET_RESULT) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HOOKSET_RESULT) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[61].Descriptor()
}

func (HOOKSET_RESULT) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[61]
}

func (x HOOKSET_RESULT) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HOOKSET_RESULT.Descriptor instead.
func (HOOKSET_RESULT) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{61}
}

// 水花类型
type LURES_SPLASH_TYPE int32

const (
	LURES_SPLASH_TYPE_LST_UNKNOWN LURES_SPLASH_TYPE = 0 // 无水花
	LURES_SPLASH_TYPE_LST_SMALL   LURES_SPLASH_TYPE = 1 // 小水花
	LURES_SPLASH_TYPE_LST_MIDDLE  LURES_SPLASH_TYPE = 2 // 中水花
	LURES_SPLASH_TYPE_LST_BIG     LURES_SPLASH_TYPE = 3 // 大水花
)

// Enum value maps for LURES_SPLASH_TYPE.
var (
	LURES_SPLASH_TYPE_name = map[int32]string{
		0: "LST_UNKNOWN",
		1: "LST_SMALL",
		2: "LST_MIDDLE",
		3: "LST_BIG",
	}
	LURES_SPLASH_TYPE_value = map[string]int32{
		"LST_UNKNOWN": 0,
		"LST_SMALL":   1,
		"LST_MIDDLE":  2,
		"LST_BIG":     3,
	}
)

func (x LURES_SPLASH_TYPE) Enum() *LURES_SPLASH_TYPE {
	p := new(LURES_SPLASH_TYPE)
	*p = x
	return p
}

func (x LURES_SPLASH_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LURES_SPLASH_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[62].Descriptor()
}

func (LURES_SPLASH_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[62]
}

func (x LURES_SPLASH_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LURES_SPLASH_TYPE.Descriptor instead.
func (LURES_SPLASH_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{62}
}

// 甩钩类型
type UNHOOK_TYPE int32

const (
	UNHOOK_TYPE_UHT_UNKNOWN UNHOOK_TYPE = 0 // 无
	UNHOOK_TYPE_UHT_NO      UNHOOK_TYPE = 1 // 否
	UNHOOK_TYPE_UHT_YES     UNHOOK_TYPE = 2 // 是
)

// Enum value maps for UNHOOK_TYPE.
var (
	UNHOOK_TYPE_name = map[int32]string{
		0: "UHT_UNKNOWN",
		1: "UHT_NO",
		2: "UHT_YES",
	}
	UNHOOK_TYPE_value = map[string]int32{
		"UHT_UNKNOWN": 0,
		"UHT_NO":      1,
		"UHT_YES":     2,
	}
)

func (x UNHOOK_TYPE) Enum() *UNHOOK_TYPE {
	p := new(UNHOOK_TYPE)
	*p = x
	return p
}

func (x UNHOOK_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UNHOOK_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[63].Descriptor()
}

func (UNHOOK_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[63]
}

func (x UNHOOK_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UNHOOK_TYPE.Descriptor instead.
func (UNHOOK_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{63}
}

// 鱼饵在水中悬停类型（目前针对于路亚饵）
type BAIT_HOVER_TYPE int32

const (
	BAIT_HOVER_TYPE_BHT_SURFACE BAIT_HOVER_TYPE = 0  // 水面
	BAIT_HOVER_TYPE_BHT_BED     BAIT_HOVER_TYPE = -1 // 水底
	BAIT_HOVER_TYPE_BHT_COURSE1 BAIT_HOVER_TYPE = 1  // 水层1
	BAIT_HOVER_TYPE_BHT_COURSE2 BAIT_HOVER_TYPE = 2  // 水层2
	BAIT_HOVER_TYPE_BHT_COURSE3 BAIT_HOVER_TYPE = 3  // 水层3
)

// Enum value maps for BAIT_HOVER_TYPE.
var (
	BAIT_HOVER_TYPE_name = map[int32]string{
		0:  "BHT_SURFACE",
		-1: "BHT_BED",
		1:  "BHT_COURSE1",
		2:  "BHT_COURSE2",
		3:  "BHT_COURSE3",
	}
	BAIT_HOVER_TYPE_value = map[string]int32{
		"BHT_SURFACE": 0,
		"BHT_BED":     -1,
		"BHT_COURSE1": 1,
		"BHT_COURSE2": 2,
		"BHT_COURSE3": 3,
	}
)

func (x BAIT_HOVER_TYPE) Enum() *BAIT_HOVER_TYPE {
	p := new(BAIT_HOVER_TYPE)
	*p = x
	return p
}

func (x BAIT_HOVER_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BAIT_HOVER_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[64].Descriptor()
}

func (BAIT_HOVER_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[64]
}

func (x BAIT_HOVER_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BAIT_HOVER_TYPE.Descriptor instead.
func (BAIT_HOVER_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{64}
}

// 渔具出售类型
type TACKLE_SALE_TYPE int32

const (
	TACKLE_SALE_TYPE_TST_NOT_SALE TACKLE_SALE_TYPE = 0 // 不可出售
	TACKLE_SALE_TYPE_TST_FREE     TACKLE_SALE_TYPE = 1 // 免费出售
	TACKLE_SALE_TYPE_TST_REDUCE   TACKLE_SALE_TYPE = 2 // 折价出售
	TACKLE_SALE_TYPE_TST_ORIGINAL TACKLE_SALE_TYPE = 3 // 原价出售
)

// Enum value maps for TACKLE_SALE_TYPE.
var (
	TACKLE_SALE_TYPE_name = map[int32]string{
		0: "TST_NOT_SALE",
		1: "TST_FREE",
		2: "TST_REDUCE",
		3: "TST_ORIGINAL",
	}
	TACKLE_SALE_TYPE_value = map[string]int32{
		"TST_NOT_SALE": 0,
		"TST_FREE":     1,
		"TST_REDUCE":   2,
		"TST_ORIGINAL": 3,
	}
)

func (x TACKLE_SALE_TYPE) Enum() *TACKLE_SALE_TYPE {
	p := new(TACKLE_SALE_TYPE)
	*p = x
	return p
}

func (x TACKLE_SALE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TACKLE_SALE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[65].Descriptor()
}

func (TACKLE_SALE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[65]
}

func (x TACKLE_SALE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TACKLE_SALE_TYPE.Descriptor instead.
func (TACKLE_SALE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{65}
}

// 商城展示样式(客户端使用)
type STORE_SHOW_STYLE int32

const (
	STORE_SHOW_STYLE_STRT_NORMAL    STORE_SHOW_STYLE = 0 // 普通样式(默认)
	STORE_SHOW_STYLE_STRT_AD        STORE_SHOW_STYLE = 1 // 广告样式
	STORE_SHOW_STYLE_STRT_RECOMMEND STORE_SHOW_STYLE = 2 // 推荐样式
	STORE_SHOW_STYLE_STRT_ROOM      STORE_SHOW_STYLE = 3 // 房间样式
	STORE_SHOW_STYLE_STRT_ROD_PACK  STORE_SHOW_STYLE = 4 // 鱼竿架商场样式
)

// Enum value maps for STORE_SHOW_STYLE.
var (
	STORE_SHOW_STYLE_name = map[int32]string{
		0: "STRT_NORMAL",
		1: "STRT_AD",
		2: "STRT_RECOMMEND",
		3: "STRT_ROOM",
		4: "STRT_ROD_PACK",
	}
	STORE_SHOW_STYLE_value = map[string]int32{
		"STRT_NORMAL":    0,
		"STRT_AD":        1,
		"STRT_RECOMMEND": 2,
		"STRT_ROOM":      3,
		"STRT_ROD_PACK":  4,
	}
)

func (x STORE_SHOW_STYLE) Enum() *STORE_SHOW_STYLE {
	p := new(STORE_SHOW_STYLE)
	*p = x
	return p
}

func (x STORE_SHOW_STYLE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (STORE_SHOW_STYLE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[66].Descriptor()
}

func (STORE_SHOW_STYLE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[66]
}

func (x STORE_SHOW_STYLE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use STORE_SHOW_STYLE.Descriptor instead.
func (STORE_SHOW_STYLE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{66}
}

// 钓场事件类型
type POND_EVENT_CHANGE_TYPE int32

const (
	POND_EVENT_CHANGE_TYPE_PECV_UNKNOWN       POND_EVENT_CHANGE_TYPE = 0
	POND_EVENT_CHANGE_TYPE_PECV_EX_LEVEL      POND_EVENT_CHANGE_TYPE = 1 // 等级变化
	POND_EVENT_CHANGE_TYPE_PECV_ENERGY_CHANGE POND_EVENT_CHANGE_TYPE = 2 // 体力变化
)

// Enum value maps for POND_EVENT_CHANGE_TYPE.
var (
	POND_EVENT_CHANGE_TYPE_name = map[int32]string{
		0: "PECV_UNKNOWN",
		1: "PECV_EX_LEVEL",
		2: "PECV_ENERGY_CHANGE",
	}
	POND_EVENT_CHANGE_TYPE_value = map[string]int32{
		"PECV_UNKNOWN":       0,
		"PECV_EX_LEVEL":      1,
		"PECV_ENERGY_CHANGE": 2,
	}
)

func (x POND_EVENT_CHANGE_TYPE) Enum() *POND_EVENT_CHANGE_TYPE {
	p := new(POND_EVENT_CHANGE_TYPE)
	*p = x
	return p
}

func (x POND_EVENT_CHANGE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (POND_EVENT_CHANGE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[67].Descriptor()
}

func (POND_EVENT_CHANGE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[67]
}

func (x POND_EVENT_CHANGE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use POND_EVENT_CHANGE_TYPE.Descriptor instead.
func (POND_EVENT_CHANGE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{67}
}

// 旅途背包类型
type TRIP_BAG_TYPE int32

const (
	TRIP_BAG_TYPE_TBT_UNKNOWN      TRIP_BAG_TYPE = 0
	TRIP_BAG_TYPE_TBT_FISHING_GEAR TRIP_BAG_TYPE = 1 // 渔具包
	TRIP_BAG_TYPE_TBT_FOOD         TRIP_BAG_TYPE = 2 // 食物盒
)

// Enum value maps for TRIP_BAG_TYPE.
var (
	TRIP_BAG_TYPE_name = map[int32]string{
		0: "TBT_UNKNOWN",
		1: "TBT_FISHING_GEAR",
		2: "TBT_FOOD",
	}
	TRIP_BAG_TYPE_value = map[string]int32{
		"TBT_UNKNOWN":      0,
		"TBT_FISHING_GEAR": 1,
		"TBT_FOOD":         2,
	}
)

func (x TRIP_BAG_TYPE) Enum() *TRIP_BAG_TYPE {
	p := new(TRIP_BAG_TYPE)
	*p = x
	return p
}

func (x TRIP_BAG_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TRIP_BAG_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[68].Descriptor()
}

func (TRIP_BAG_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[68]
}

func (x TRIP_BAG_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TRIP_BAG_TYPE.Descriptor instead.
func (TRIP_BAG_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{68}
}

// 旅途背包操作
type TRIP_BAG_OPERATE int32

const (
	TRIP_BAG_OPERATE_TBO_UNKNOWN TRIP_BAG_OPERATE = 0
	TRIP_BAG_OPERATE_TBO_IN      TRIP_BAG_OPERATE = 1 // 移入
	TRIP_BAG_OPERATE_TBO_OUT     TRIP_BAG_OPERATE = 2 // 移出
)

// Enum value maps for TRIP_BAG_OPERATE.
var (
	TRIP_BAG_OPERATE_name = map[int32]string{
		0: "TBO_UNKNOWN",
		1: "TBO_IN",
		2: "TBO_OUT",
	}
	TRIP_BAG_OPERATE_value = map[string]int32{
		"TBO_UNKNOWN": 0,
		"TBO_IN":      1,
		"TBO_OUT":     2,
	}
)

func (x TRIP_BAG_OPERATE) Enum() *TRIP_BAG_OPERATE {
	p := new(TRIP_BAG_OPERATE)
	*p = x
	return p
}

func (x TRIP_BAG_OPERATE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TRIP_BAG_OPERATE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[69].Descriptor()
}

func (TRIP_BAG_OPERATE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[69]
}

func (x TRIP_BAG_OPERATE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TRIP_BAG_OPERATE.Descriptor instead.
func (TRIP_BAG_OPERATE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{69}
}

// 旅途钓组位置
type TRIP_ROD_SIT int32

const (
	TRIP_ROD_SIT_TRS_UNKNOWN TRIP_ROD_SIT = 0
	TRIP_ROD_SIT_TRS_ROD     TRIP_ROD_SIT = 1 // 鱼竿
	TRIP_ROD_SIT_TRS_REEL    TRIP_ROD_SIT = 2 // 鱼轮
	TRIP_ROD_SIT_TRS_LINE    TRIP_ROD_SIT = 3 // 主线
	TRIP_ROD_SIT_TRS_LEADER  TRIP_ROD_SIT = 4 // 子线
	TRIP_ROD_SIT_TRS_BAIT    TRIP_ROD_SIT = 5 // 鱼饵
	TRIP_ROD_SIT_TRS_BOBBER  TRIP_ROD_SIT = 6 // 浮漂
	TRIP_ROD_SIT_TRS_HOOKS   TRIP_ROD_SIT = 7 // 鱼钩
	TRIP_ROD_SIT_TRS_SINKERS TRIP_ROD_SIT = 8 // 铅坠
)

// Enum value maps for TRIP_ROD_SIT.
var (
	TRIP_ROD_SIT_name = map[int32]string{
		0: "TRS_UNKNOWN",
		1: "TRS_ROD",
		2: "TRS_REEL",
		3: "TRS_LINE",
		4: "TRS_LEADER",
		5: "TRS_BAIT",
		6: "TRS_BOBBER",
		7: "TRS_HOOKS",
		8: "TRS_SINKERS",
	}
	TRIP_ROD_SIT_value = map[string]int32{
		"TRS_UNKNOWN": 0,
		"TRS_ROD":     1,
		"TRS_REEL":    2,
		"TRS_LINE":    3,
		"TRS_LEADER":  4,
		"TRS_BAIT":    5,
		"TRS_BOBBER":  6,
		"TRS_HOOKS":   7,
		"TRS_SINKERS": 8,
	}
)

func (x TRIP_ROD_SIT) Enum() *TRIP_ROD_SIT {
	p := new(TRIP_ROD_SIT)
	*p = x
	return p
}

func (x TRIP_ROD_SIT) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TRIP_ROD_SIT) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[70].Descriptor()
}

func (TRIP_ROD_SIT) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[70]
}

func (x TRIP_ROD_SIT) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TRIP_ROD_SIT.Descriptor instead.
func (TRIP_ROD_SIT) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{70}
}

type FISH_SIZE_TYPE int32

const (
	FISH_SIZE_TYPE_SIZE_UNKNOWN FISH_SIZE_TYPE = 0
	FISH_SIZE_TYPE_SIZE_YOUNG   FISH_SIZE_TYPE = 1 // 幼年
	FISH_SIZE_TYPE_SIZE_ADULT   FISH_SIZE_TYPE = 2 // 成年
	FISH_SIZE_TYPE_SIZE_PRIZE   FISH_SIZE_TYPE = 3 // 奖杯
	FISH_SIZE_TYPE_SIZE_SPECIAL FISH_SIZE_TYPE = 4 // 特殊
	FISH_SIZE_TYPE_SIZE_LEGEND  FISH_SIZE_TYPE = 5 // 传说
)

// Enum value maps for FISH_SIZE_TYPE.
var (
	FISH_SIZE_TYPE_name = map[int32]string{
		0: "SIZE_UNKNOWN",
		1: "SIZE_YOUNG",
		2: "SIZE_ADULT",
		3: "SIZE_PRIZE",
		4: "SIZE_SPECIAL",
		5: "SIZE_LEGEND",
	}
	FISH_SIZE_TYPE_value = map[string]int32{
		"SIZE_UNKNOWN": 0,
		"SIZE_YOUNG":   1,
		"SIZE_ADULT":   2,
		"SIZE_PRIZE":   3,
		"SIZE_SPECIAL": 4,
		"SIZE_LEGEND":  5,
	}
)

func (x FISH_SIZE_TYPE) Enum() *FISH_SIZE_TYPE {
	p := new(FISH_SIZE_TYPE)
	*p = x
	return p
}

func (x FISH_SIZE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_SIZE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[71].Descriptor()
}

func (FISH_SIZE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[71]
}

func (x FISH_SIZE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_SIZE_TYPE.Descriptor instead.
func (FISH_SIZE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{71}
}

// 道具品牌
type TACKLE_BRAND_TYPE int32

const (
	TACKLE_BRAND_TYPE_TACKLEBT_UNKNOWN TACKLE_BRAND_TYPE = 0
	TACKLE_BRAND_TYPE_TACKLEBT_KF      TACKLE_BRAND_TYPE = 1 // KF
	TACKLE_BRAND_TYPE_TACKLEBT_INSECT  TACKLE_BRAND_TYPE = 2 // 昆虫
	TACKLE_BRAND_TYPE_TACKLEBT_TORAY   TACKLE_BRAND_TYPE = 3 // 东丽
	TACKLE_BRAND_TYPE_TACKLEBT_YGK     TACKLE_BRAND_TYPE = 4 // YGK
	TACKLE_BRAND_TYPE_TACKLEBT_SIEGEL  TACKLE_BRAND_TYPE = 5 // 西格
)

// Enum value maps for TACKLE_BRAND_TYPE.
var (
	TACKLE_BRAND_TYPE_name = map[int32]string{
		0: "TACKLEBT_UNKNOWN",
		1: "TACKLEBT_KF",
		2: "TACKLEBT_INSECT",
		3: "TACKLEBT_TORAY",
		4: "TACKLEBT_YGK",
		5: "TACKLEBT_SIEGEL",
	}
	TACKLE_BRAND_TYPE_value = map[string]int32{
		"TACKLEBT_UNKNOWN": 0,
		"TACKLEBT_KF":      1,
		"TACKLEBT_INSECT":  2,
		"TACKLEBT_TORAY":   3,
		"TACKLEBT_YGK":     4,
		"TACKLEBT_SIEGEL":  5,
	}
)

func (x TACKLE_BRAND_TYPE) Enum() *TACKLE_BRAND_TYPE {
	p := new(TACKLE_BRAND_TYPE)
	*p = x
	return p
}

func (x TACKLE_BRAND_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TACKLE_BRAND_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[72].Descriptor()
}

func (TACKLE_BRAND_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[72]
}

func (x TACKLE_BRAND_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TACKLE_BRAND_TYPE.Descriptor instead.
func (TACKLE_BRAND_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{72}
}

// 道具品牌系列
type TACKLE_BRAND_SERIES_TYPE int32

const (
	TACKLE_BRAND_SERIES_TYPE_TBST_UNKNOWN      TACKLE_BRAND_SERIES_TYPE = 0
	TACKLE_BRAND_SERIES_TYPE_TBST_ROLLFISH     TACKLE_BRAND_SERIES_TYPE = 1
	TACKLE_BRAND_SERIES_TYPE_TBST_CREST        TACKLE_BRAND_SERIES_TYPE = 2
	TACKLE_BRAND_SERIES_TYPE_TBST_ATHLON       TACKLE_BRAND_SERIES_TYPE = 3
	TACKLE_BRAND_SERIES_TYPE_TBST_DEMONHUNTER  TACKLE_BRAND_SERIES_TYPE = 4
	TACKLE_BRAND_SERIES_TYPE_TBST_CORSSFIRE    TACKLE_BRAND_SERIES_TYPE = 5
	TACKLE_BRAND_SERIES_TYPE_TBST_EXPRIDE      TACKLE_BRAND_SERIES_TYPE = 6
	TACKLE_BRAND_SERIES_TYPE_TBST_PMAXSX       TACKLE_BRAND_SERIES_TYPE = 7
	TACKLE_BRAND_SERIES_TYPE_TBST_VERITAS      TACKLE_BRAND_SERIES_TYPE = 8
	TACKLE_BRAND_SERIES_TYPE_TBST_GREATHUNTING TACKLE_BRAND_SERIES_TYPE = 9
	TACKLE_BRAND_SERIES_TYPE_TBST_GIANTWAVES   TACKLE_BRAND_SERIES_TYPE = 10
	TACKLE_BRAND_SERIES_TYPE_TBST_SHADOW       TACKLE_BRAND_SERIES_TYPE = 11
	TACKLE_BRAND_SERIES_TYPE_TBST_CQBFS        TACKLE_BRAND_SERIES_TYPE = 12
	TACKLE_BRAND_SERIES_TYPE_TBST_CARDIFF      TACKLE_BRAND_SERIES_TYPE = 13
	TACKLE_BRAND_SERIES_TYPE_TBST_MAX4         TACKLE_BRAND_SERIES_TYPE = 14
	TACKLE_BRAND_SERIES_TYPE_TBST_NYLON        TACKLE_BRAND_SERIES_TYPE = 15
	TACKLE_BRAND_SERIES_TYPE_TBST_UPGRADE      TACKLE_BRAND_SERIES_TYPE = 16
	TACKLE_BRAND_SERIES_TYPE_TBST_LONG         TACKLE_BRAND_SERIES_TYPE = 17
	TACKLE_BRAND_SERIES_TYPE_TBST_CLASSICLONG  TACKLE_BRAND_SERIES_TYPE = 18
	TACKLE_BRAND_SERIES_TYPE_TBST_MAYFLY       TACKLE_BRAND_SERIES_TYPE = 19
	TACKLE_BRAND_SERIES_TYPE_TBST_SHAD         TACKLE_BRAND_SERIES_TYPE = 20
	TACKLE_BRAND_SERIES_TYPE_TBST_GRUB         TACKLE_BRAND_SERIES_TYPE = 21
	TACKLE_BRAND_SERIES_TYPE_TBST_POPPER       TACKLE_BRAND_SERIES_TYPE = 22
	TACKLE_BRAND_SERIES_TYPE_TBST_MINNOW       TACKLE_BRAND_SERIES_TYPE = 23
	TACKLE_BRAND_SERIES_TYPE_TBST_SPOON        TACKLE_BRAND_SERIES_TYPE = 24
	TACKLE_BRAND_SERIES_TYPE_TBST_SPINNER      TACKLE_BRAND_SERIES_TYPE = 25
	TACKLE_BRAND_SERIES_TYPE_TBST_CHKHOOK      TACKLE_BRAND_SERIES_TYPE = 26
)

// Enum value maps for TACKLE_BRAND_SERIES_TYPE.
var (
	TACKLE_BRAND_SERIES_TYPE_name = map[int32]string{
		0:  "TBST_UNKNOWN",
		1:  "TBST_ROLLFISH",
		2:  "TBST_CREST",
		3:  "TBST_ATHLON",
		4:  "TBST_DEMONHUNTER",
		5:  "TBST_CORSSFIRE",
		6:  "TBST_EXPRIDE",
		7:  "TBST_PMAXSX",
		8:  "TBST_VERITAS",
		9:  "TBST_GREATHUNTING",
		10: "TBST_GIANTWAVES",
		11: "TBST_SHADOW",
		12: "TBST_CQBFS",
		13: "TBST_CARDIFF",
		14: "TBST_MAX4",
		15: "TBST_NYLON",
		16: "TBST_UPGRADE",
		17: "TBST_LONG",
		18: "TBST_CLASSICLONG",
		19: "TBST_MAYFLY",
		20: "TBST_SHAD",
		21: "TBST_GRUB",
		22: "TBST_POPPER",
		23: "TBST_MINNOW",
		24: "TBST_SPOON",
		25: "TBST_SPINNER",
		26: "TBST_CHKHOOK",
	}
	TACKLE_BRAND_SERIES_TYPE_value = map[string]int32{
		"TBST_UNKNOWN":      0,
		"TBST_ROLLFISH":     1,
		"TBST_CREST":        2,
		"TBST_ATHLON":       3,
		"TBST_DEMONHUNTER":  4,
		"TBST_CORSSFIRE":    5,
		"TBST_EXPRIDE":      6,
		"TBST_PMAXSX":       7,
		"TBST_VERITAS":      8,
		"TBST_GREATHUNTING": 9,
		"TBST_GIANTWAVES":   10,
		"TBST_SHADOW":       11,
		"TBST_CQBFS":        12,
		"TBST_CARDIFF":      13,
		"TBST_MAX4":         14,
		"TBST_NYLON":        15,
		"TBST_UPGRADE":      16,
		"TBST_LONG":         17,
		"TBST_CLASSICLONG":  18,
		"TBST_MAYFLY":       19,
		"TBST_SHAD":         20,
		"TBST_GRUB":         21,
		"TBST_POPPER":       22,
		"TBST_MINNOW":       23,
		"TBST_SPOON":        24,
		"TBST_SPINNER":      25,
		"TBST_CHKHOOK":      26,
	}
)

func (x TACKLE_BRAND_SERIES_TYPE) Enum() *TACKLE_BRAND_SERIES_TYPE {
	p := new(TACKLE_BRAND_SERIES_TYPE)
	*p = x
	return p
}

func (x TACKLE_BRAND_SERIES_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TACKLE_BRAND_SERIES_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[73].Descriptor()
}

func (TACKLE_BRAND_SERIES_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[73]
}

func (x TACKLE_BRAND_SERIES_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TACKLE_BRAND_SERIES_TYPE.Descriptor instead.
func (TACKLE_BRAND_SERIES_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{73}
}

// ************************************************************************
type PAY_TYPE int32

const (
	PAY_TYPE_PT_UNKNOWN PAY_TYPE = 0
	PAY_TYPE_PT_SANDBOX PAY_TYPE = 1 // 测试支付
	PAY_TYPE_PT_WECHAT  PAY_TYPE = 2
	PAY_TYPE_PT_ALIPAY  PAY_TYPE = 3
	PAY_TYPE_PT_APPLE   PAY_TYPE = 4 // [支付渠道]APP_STORE
	PAY_TYPE_PT_GOOGLE  PAY_TYPE = 5 // [支付渠道]GOOGLE_PLAY
)

// Enum value maps for PAY_TYPE.
var (
	PAY_TYPE_name = map[int32]string{
		0: "PT_UNKNOWN",
		1: "PT_SANDBOX",
		2: "PT_WECHAT",
		3: "PT_ALIPAY",
		4: "PT_APPLE",
		5: "PT_GOOGLE",
	}
	PAY_TYPE_value = map[string]int32{
		"PT_UNKNOWN": 0,
		"PT_SANDBOX": 1,
		"PT_WECHAT":  2,
		"PT_ALIPAY":  3,
		"PT_APPLE":   4,
		"PT_GOOGLE":  5,
	}
)

func (x PAY_TYPE) Enum() *PAY_TYPE {
	p := new(PAY_TYPE)
	*p = x
	return p
}

func (x PAY_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PAY_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[74].Descriptor()
}

func (PAY_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[74]
}

func (x PAY_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PAY_TYPE.Descriptor instead.
func (PAY_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{74}
}

// 购买限制类型
type BUY_LIMIT_TYPE int32

const (
	BUY_LIMIT_TYPE_BLT_NONE    BUY_LIMIT_TYPE = 0 // 无限制
	BUY_LIMIT_TYPE_BLT_DAY     BUY_LIMIT_TYPE = 1 // 按天限制
	BUY_LIMIT_TYPE_BLT_WEEK    BUY_LIMIT_TYPE = 2 // 按周限制
	BUY_LIMIT_TYPE_BLT_MONTH   BUY_LIMIT_TYPE = 3 // 按月限制
	BUY_LIMIT_TYPE_BLT_FOREVER BUY_LIMIT_TYPE = 4 // 永久限制
)

// Enum value maps for BUY_LIMIT_TYPE.
var (
	BUY_LIMIT_TYPE_name = map[int32]string{
		0: "BLT_NONE",
		1: "BLT_DAY",
		2: "BLT_WEEK",
		3: "BLT_MONTH",
		4: "BLT_FOREVER",
	}
	BUY_LIMIT_TYPE_value = map[string]int32{
		"BLT_NONE":    0,
		"BLT_DAY":     1,
		"BLT_WEEK":    2,
		"BLT_MONTH":   3,
		"BLT_FOREVER": 4,
	}
)

func (x BUY_LIMIT_TYPE) Enum() *BUY_LIMIT_TYPE {
	p := new(BUY_LIMIT_TYPE)
	*p = x
	return p
}

func (x BUY_LIMIT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BUY_LIMIT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[75].Descriptor()
}

func (BUY_LIMIT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[75]
}

func (x BUY_LIMIT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BUY_LIMIT_TYPE.Descriptor instead.
func (BUY_LIMIT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{75}
}

// 支付入口场景类型
type PURCHASE_ENTRANCE_TYPE int32

const (
	PURCHASE_ENTRANCE_TYPE_PET_UNKNOWN        PURCHASE_ENTRANCE_TYPE = 0
	PURCHASE_ENTRANCE_TYPE_PET_LOBBY_STORE    PURCHASE_ENTRANCE_TYPE = 1 // 商城
	PURCHASE_ENTRANCE_TYPE_PET_LOBBY_TASK     PURCHASE_ENTRANCE_TYPE = 2 // 任务
	PURCHASE_ENTRANCE_TYPE_PET_LOBBY_ACTIVITY PURCHASE_ENTRANCE_TYPE = 3 // 活动
	PURCHASE_ENTRANCE_TYPE_PET_LOBBY_GAME     PURCHASE_ENTRANCE_TYPE = 4 // 游戏
	PURCHASE_ENTRANCE_TYPE_PET_LOBBY_OTHER    PURCHASE_ENTRANCE_TYPE = 5 // 其它
)

// Enum value maps for PURCHASE_ENTRANCE_TYPE.
var (
	PURCHASE_ENTRANCE_TYPE_name = map[int32]string{
		0: "PET_UNKNOWN",
		1: "PET_LOBBY_STORE",
		2: "PET_LOBBY_TASK",
		3: "PET_LOBBY_ACTIVITY",
		4: "PET_LOBBY_GAME",
		5: "PET_LOBBY_OTHER",
	}
	PURCHASE_ENTRANCE_TYPE_value = map[string]int32{
		"PET_UNKNOWN":        0,
		"PET_LOBBY_STORE":    1,
		"PET_LOBBY_TASK":     2,
		"PET_LOBBY_ACTIVITY": 3,
		"PET_LOBBY_GAME":     4,
		"PET_LOBBY_OTHER":    5,
	}
)

func (x PURCHASE_ENTRANCE_TYPE) Enum() *PURCHASE_ENTRANCE_TYPE {
	p := new(PURCHASE_ENTRANCE_TYPE)
	*p = x
	return p
}

func (x PURCHASE_ENTRANCE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PURCHASE_ENTRANCE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[76].Descriptor()
}

func (PURCHASE_ENTRANCE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[76]
}

func (x PURCHASE_ENTRANCE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PURCHASE_ENTRANCE_TYPE.Descriptor instead.
func (PURCHASE_ENTRANCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{76}
}

// 触发支付原因
type PURCHASE_TRIGGER_TYPE int32

const (
	PURCHASE_TRIGGER_TYPE_PTT_UNKNOWN               PURCHASE_TRIGGER_TYPE = 0
	PURCHASE_TRIGGER_TYPE_PTT_INITIATIVE_CLICK      PURCHASE_TRIGGER_TYPE = 1 // 主动点击
	PURCHASE_TRIGGER_TYPE_PTT_LACK_SPIN_POPUP       PURCHASE_TRIGGER_TYPE = 2 // 缺体力弹出
	PURCHASE_TRIGGER_TYPE_PTT_LACK_COIN_POPUP       PURCHASE_TRIGGER_TYPE = 3 // 缺金币弹出
	PURCHASE_TRIGGER_TYPE_PTT_LACK_DIAMOND_POPUP    PURCHASE_TRIGGER_TYPE = 4 // 缺钻石弹出
	PURCHASE_TRIGGER_TYPE_PTT_LOGIN_AUTO_POPUP      PURCHASE_TRIGGER_TYPE = 5 // 登录自动弹
	PURCHASE_TRIGGER_TYPE_PTT_MODULE_AUTO_POPUP     PURCHASE_TRIGGER_TYPE = 6 // 功能自动弹(如鱼护满提示扩容弹出)
	PURCHASE_TRIGGER_TYPE_PTT_MODULE_ACTIVITY_POPUP PURCHASE_TRIGGER_TYPE = 7 // 活动触发
	PURCHASE_TRIGGER_TYPE_PTT_TOP_UP_ACTIVITY       PURCHASE_TRIGGER_TYPE = 8 // 充值活动
)

// Enum value maps for PURCHASE_TRIGGER_TYPE.
var (
	PURCHASE_TRIGGER_TYPE_name = map[int32]string{
		0: "PTT_UNKNOWN",
		1: "PTT_INITIATIVE_CLICK",
		2: "PTT_LACK_SPIN_POPUP",
		3: "PTT_LACK_COIN_POPUP",
		4: "PTT_LACK_DIAMOND_POPUP",
		5: "PTT_LOGIN_AUTO_POPUP",
		6: "PTT_MODULE_AUTO_POPUP",
		7: "PTT_MODULE_ACTIVITY_POPUP",
		8: "PTT_TOP_UP_ACTIVITY",
	}
	PURCHASE_TRIGGER_TYPE_value = map[string]int32{
		"PTT_UNKNOWN":               0,
		"PTT_INITIATIVE_CLICK":      1,
		"PTT_LACK_SPIN_POPUP":       2,
		"PTT_LACK_COIN_POPUP":       3,
		"PTT_LACK_DIAMOND_POPUP":    4,
		"PTT_LOGIN_AUTO_POPUP":      5,
		"PTT_MODULE_AUTO_POPUP":     6,
		"PTT_MODULE_ACTIVITY_POPUP": 7,
		"PTT_TOP_UP_ACTIVITY":       8,
	}
)

func (x PURCHASE_TRIGGER_TYPE) Enum() *PURCHASE_TRIGGER_TYPE {
	p := new(PURCHASE_TRIGGER_TYPE)
	*p = x
	return p
}

func (x PURCHASE_TRIGGER_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PURCHASE_TRIGGER_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[77].Descriptor()
}

func (PURCHASE_TRIGGER_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[77]
}

func (x PURCHASE_TRIGGER_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PURCHASE_TRIGGER_TYPE.Descriptor instead.
func (PURCHASE_TRIGGER_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{77}
}

// 支付失败原因，[0-7]为Unity Purchaseing Service返回的PurchaseFailureReason结果
// [8+]的为项目中的支付流程异常问题
type PURCHASE_FAILED_TYPE int32

const (
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_PURCHASING_UNAVAILABLE    PURCHASE_FAILED_TYPE = 0  // 支付功能不可使用。The system purchasing feature is unavailable.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_EXISTING_PURCHASE_PENDING PURCHASE_FAILED_TYPE = 1  // 支付请求一个商品时，已经有一个对应商品正在支付中。A purchase was already in progress when a new purchase was requested.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_PRODUCT_UNAVAILABLE       PURCHASE_FAILED_TYPE = 2  // 该商品不可使用，在第三方商店中找不到。The product is not available to purchase on the store.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_SIGNATURE_INVALID         PURCHASE_FAILED_TYPE = 3  // 购买的收据签名验证失败。Signature validation of the purchase's receipt failed.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_USER_CANCELLED            PURCHASE_FAILED_TYPE = 4  // 用户手动取消支付。The user opted to cancel rather than proceed with the purchase.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_PAYMENT_DECLINED          PURCHASE_FAILED_TYPE = 5  // 付款存在异常问题。There was a problem with the payment.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_DUPLICATE_TRANSACTION     PURCHASE_FAILED_TYPE = 6  // 交易完成时，出现重复的交易。A duplicate transaction error when the transaction has already been completed successfully.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_UNKNOWN                   PURCHASE_FAILED_TYPE = 7  // 未知异常。A catch-all for unrecognized purchase problems.
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_CREATE_ORDER_FAILED       PURCHASE_FAILED_TYPE = 8  // 下单失败
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_INVALID_RECEIPT           PURCHASE_FAILED_TYPE = 9  // 无效的收据凭证,空收据
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_DELIVER_FAILED            PURCHASE_FAILED_TYPE = 10 // 发货失败
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_SERVICE_NO_INITIALIZE     PURCHASE_FAILED_TYPE = 11 // 支付服务模块初始化失败
	PURCHASE_FAILED_TYPE_PURCHASE_FAILED_PURCHASING                PURCHASE_FAILED_TYPE = 12 // 正在支付中
)

// Enum value maps for PURCHASE_FAILED_TYPE.
var (
	PURCHASE_FAILED_TYPE_name = map[int32]string{
		0:  "PURCHASE_FAILED_PURCHASING_UNAVAILABLE",
		1:  "PURCHASE_FAILED_EXISTING_PURCHASE_PENDING",
		2:  "PURCHASE_FAILED_PRODUCT_UNAVAILABLE",
		3:  "PURCHASE_FAILED_SIGNATURE_INVALID",
		4:  "PURCHASE_FAILED_USER_CANCELLED",
		5:  "PURCHASE_FAILED_PAYMENT_DECLINED",
		6:  "PURCHASE_FAILED_DUPLICATE_TRANSACTION",
		7:  "PURCHASE_FAILED_UNKNOWN",
		8:  "PURCHASE_FAILED_CREATE_ORDER_FAILED",
		9:  "PURCHASE_FAILED_INVALID_RECEIPT",
		10: "PURCHASE_FAILED_DELIVER_FAILED",
		11: "PURCHASE_FAILED_SERVICE_NO_INITIALIZE",
		12: "PURCHASE_FAILED_PURCHASING",
	}
	PURCHASE_FAILED_TYPE_value = map[string]int32{
		"PURCHASE_FAILED_PURCHASING_UNAVAILABLE":    0,
		"PURCHASE_FAILED_EXISTING_PURCHASE_PENDING": 1,
		"PURCHASE_FAILED_PRODUCT_UNAVAILABLE":       2,
		"PURCHASE_FAILED_SIGNATURE_INVALID":         3,
		"PURCHASE_FAILED_USER_CANCELLED":            4,
		"PURCHASE_FAILED_PAYMENT_DECLINED":          5,
		"PURCHASE_FAILED_DUPLICATE_TRANSACTION":     6,
		"PURCHASE_FAILED_UNKNOWN":                   7,
		"PURCHASE_FAILED_CREATE_ORDER_FAILED":       8,
		"PURCHASE_FAILED_INVALID_RECEIPT":           9,
		"PURCHASE_FAILED_DELIVER_FAILED":            10,
		"PURCHASE_FAILED_SERVICE_NO_INITIALIZE":     11,
		"PURCHASE_FAILED_PURCHASING":                12,
	}
)

func (x PURCHASE_FAILED_TYPE) Enum() *PURCHASE_FAILED_TYPE {
	p := new(PURCHASE_FAILED_TYPE)
	*p = x
	return p
}

func (x PURCHASE_FAILED_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PURCHASE_FAILED_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[78].Descriptor()
}

func (PURCHASE_FAILED_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[78]
}

func (x PURCHASE_FAILED_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PURCHASE_FAILED_TYPE.Descriptor instead.
func (PURCHASE_FAILED_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{78}
}

// 支付订单状态
type PURCHASE_BILL_STATUS int32

const (
	PURCHASE_BILL_STATUS_PBS_UNKNOWN            PURCHASE_BILL_STATUS = 0  //未知
	PURCHASE_BILL_STATUS_PBS_PLACE              PURCHASE_BILL_STATUS = 1  //新下单
	PURCHASE_BILL_STATUS_PBS_CANCEL             PURCHASE_BILL_STATUS = 2  //已取消
	PURCHASE_BILL_STATUS_PBS_PLACE_ANEW         PURCHASE_BILL_STATUS = 3  //重新下单（取消未支付订单）
	PURCHASE_BILL_STATUS_PBS_PAID               PURCHASE_BILL_STATUS = 4  //已支付
	PURCHASE_BILL_STATUS_PBS_VERIFY_FAILURE     PURCHASE_BILL_STATUS = 5  //（校验）校验失败
	PURCHASE_BILL_STATUS_PBS_VERIFY_PAID_CANCEL PURCHASE_BILL_STATUS = 6  //（校验）支付取消
	PURCHASE_BILL_STATUS_PBS_VERIFY_CONSUMPTION PURCHASE_BILL_STATUS = 7  //（校验）已消费
	PURCHASE_BILL_STATUS_PBS_VERIFY_SUCCESS     PURCHASE_BILL_STATUS = 8  //校验成功
	PURCHASE_BILL_STATUS_PBS_DELIVERED          PURCHASE_BILL_STATUS = 9  //已发货
	PURCHASE_BILL_STATUS_PBS_REPLENISHMENT      PURCHASE_BILL_STATUS = 10 //补单成功
	PURCHASE_BILL_STATUS_PBS_ABNORMAL_DELIVERY  PURCHASE_BILL_STATUS = 11 //异常订单（坏账订单）
	PURCHASE_BILL_STATUS_PBS_DELIVER_FAILURE    PURCHASE_BILL_STATUS = 12 //发货失败
	PURCHASE_BILL_STATUS_PBS_ORDER_NOT_EXIST    PURCHASE_BILL_STATUS = 13 //订单不存在
)

// Enum value maps for PURCHASE_BILL_STATUS.
var (
	PURCHASE_BILL_STATUS_name = map[int32]string{
		0:  "PBS_UNKNOWN",
		1:  "PBS_PLACE",
		2:  "PBS_CANCEL",
		3:  "PBS_PLACE_ANEW",
		4:  "PBS_PAID",
		5:  "PBS_VERIFY_FAILURE",
		6:  "PBS_VERIFY_PAID_CANCEL",
		7:  "PBS_VERIFY_CONSUMPTION",
		8:  "PBS_VERIFY_SUCCESS",
		9:  "PBS_DELIVERED",
		10: "PBS_REPLENISHMENT",
		11: "PBS_ABNORMAL_DELIVERY",
		12: "PBS_DELIVER_FAILURE",
		13: "PBS_ORDER_NOT_EXIST",
	}
	PURCHASE_BILL_STATUS_value = map[string]int32{
		"PBS_UNKNOWN":            0,
		"PBS_PLACE":              1,
		"PBS_CANCEL":             2,
		"PBS_PLACE_ANEW":         3,
		"PBS_PAID":               4,
		"PBS_VERIFY_FAILURE":     5,
		"PBS_VERIFY_PAID_CANCEL": 6,
		"PBS_VERIFY_CONSUMPTION": 7,
		"PBS_VERIFY_SUCCESS":     8,
		"PBS_DELIVERED":          9,
		"PBS_REPLENISHMENT":      10,
		"PBS_ABNORMAL_DELIVERY":  11,
		"PBS_DELIVER_FAILURE":    12,
		"PBS_ORDER_NOT_EXIST":    13,
	}
)

func (x PURCHASE_BILL_STATUS) Enum() *PURCHASE_BILL_STATUS {
	p := new(PURCHASE_BILL_STATUS)
	*p = x
	return p
}

func (x PURCHASE_BILL_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PURCHASE_BILL_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[79].Descriptor()
}

func (PURCHASE_BILL_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[79]
}

func (x PURCHASE_BILL_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PURCHASE_BILL_STATUS.Descriptor instead.
func (PURCHASE_BILL_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{79}
}

// ************************************************************************
type GM_CMD int32

const (
	GM_CMD_GC_UNKNOWN       GM_CMD = 0
	GM_CMD_GC_GAMETIME      GM_CMD = 1 // 设置游戏时间
	GM_CMD_GC_LOAD_RIG_RULE GM_CMD = 2 // 加载干架规则
	GM_CMD_GC_DOWNTIME_KICK GM_CMD = 3 // 维护踢人
	// -------------- world ----------------------
	GM_CMD_GC_WORLD_BEGIN   GM_CMD = 1000
	GM_CMD_GC_CLEAR_WEATHER GM_CMD = 1001 // 清理天气
	GM_CMD_GC_WORLD_END     GM_CMD = 1099
	// -------------- Hall ----------------------
	GM_CMD_GC_HALL_BEGIN               GM_CMD = 1100
	GM_CMD_GC_HALL_ITEM                GM_CMD = 1101
	GM_CMD_GC_HALL_CONTINUOUS_LOGIN    GM_CMD = 1102 // 连续登录奖励
	GM_CMD_GC_HALL_CREATE_CDK          GM_CMD = 1103 // cdk 创建
	GM_CMD_GC_HALL_QUERY_CDK_BATCHES   GM_CMD = 1104 // cdk 批次查询
	GM_CMD_GC_HALL_QUERY_CDK_RECORDS   GM_CMD = 1105 // cdk 使用记录查询
	GM_CMD_GC_HALL_DISABLE_CDK_BATCHES GM_CMD = 1106 // cdk 批次禁用
	GM_CMD_GC_HALL_END                 GM_CMD = 1199
	// -------------- asset ----------------------
	GM_CMD_GC_ASSET_BEGIN          GM_CMD = 1200
	GM_CMD_GC_ASSET_CLEAR_CATEGORY GM_CMD = 1201
	GM_CMD_GC_ASSET_END            GM_CMD = 1299
	// -------------- task ----------------------
	GM_CMD_GM_TASK_BEGIN        GM_CMD = 1300
	GM_CMD_GM_TASK_OPERATE      GM_CMD = 1301 // 任务操作
	GM_CMD_GM_TASK_PROGRESS_SET GM_CMD = 1302 // 设置积分进度
	GM_CMD_GM_TASK_SUB_UPDATE   GM_CMD = 1303 // 更新子任务进度
	GM_CMD_GM_TASK_END          GM_CMD = 1399
	// -------------- spot ----------------------
	GM_CMD_GM_CMD_GM_SPOT_BEGIN    GM_CMD = 1400
	GM_CMD_GM_CMD_GC_MODIFY_ENERGY GM_CMD = 1401 // 修改体力
	GM_CMD_GM_CMD_GM_SPOT_END      GM_CMD = 1499
	// -------------- hook ----------------------
	GM_CMD_GM_CMD_GM_HOOK_BEGIN        GM_CMD = 1500
	GM_CMD_GM_CMD_GC_OPERATE_HOOK_FISH GM_CMD = 1501 // 中鱼操作
	GM_CMD_GM_CMD_GM_HOOK_END          GM_CMD = 1599
	// -------------- msg ----------------------
	GM_CMD_GC_MSG_BEGIN        GM_CMD = 1600
	GM_CMD_GC_MSG_SEND_MAIL    GM_CMD = 1601 // 发送邮件
	GM_CMD_GC_MSG_BROADCAST    GM_CMD = 1610 // 广播
	GM_CMD_GC_MSG_ANN_POP_GET  GM_CMD = 1611 // 拍脸图获取
	GM_CMD_GC_MSG_ANN_POP_EDIT GM_CMD = 1612 // 拍脸图编辑
	GM_CMD_GC_MSG_ANN_POP_DEL  GM_CMD = 1613 // 拍脸图删除
	GM_CMD_GC_MSG_END          GM_CMD = 1699
	// -------------- user ----------------------
	GM_CMD_GC_USER_BEGIN             GM_CMD = 1700
	GM_CMD_GC_USER_BATCH_PLAYER_INFO GM_CMD = 1701 // 获取玩家信息请求
	GM_CMD_GC_USER_END               GM_CMD = 1799
	// -------------- GM ----------------------
	GM_CMD_GC_RANK_BEGIN       GM_CMD = 1800
	GM_CMD_GC_RANK_FLUSH_RANK  GM_CMD = 1801 // 刷新排行
	GM_CMD_GC_RANK_REWARD_RANK GM_CMD = 1802 // 强制发奖
	GM_CMD_GC_RANK_END         GM_CMD = 1899
)

// Enum value maps for GM_CMD.
var (
	GM_CMD_name = map[int32]string{
		0:    "GC_UNKNOWN",
		1:    "GC_GAMETIME",
		2:    "GC_LOAD_RIG_RULE",
		3:    "GC_DOWNTIME_KICK",
		1000: "GC_WORLD_BEGIN",
		1001: "GC_CLEAR_WEATHER",
		1099: "GC_WORLD_END",
		1100: "GC_HALL_BEGIN",
		1101: "GC_HALL_ITEM",
		1102: "GC_HALL_CONTINUOUS_LOGIN",
		1103: "GC_HALL_CREATE_CDK",
		1104: "GC_HALL_QUERY_CDK_BATCHES",
		1105: "GC_HALL_QUERY_CDK_RECORDS",
		1106: "GC_HALL_DISABLE_CDK_BATCHES",
		1199: "GC_HALL_END",
		1200: "GC_ASSET_BEGIN",
		1201: "GC_ASSET_CLEAR_CATEGORY",
		1299: "GC_ASSET_END",
		1300: "GM_TASK_BEGIN",
		1301: "GM_TASK_OPERATE",
		1302: "GM_TASK_PROGRESS_SET",
		1303: "GM_TASK_SUB_UPDATE",
		1399: "GM_TASK_END",
		1400: "GM_CMD_GM_SPOT_BEGIN",
		1401: "GM_CMD_GC_MODIFY_ENERGY",
		1499: "GM_CMD_GM_SPOT_END",
		1500: "GM_CMD_GM_HOOK_BEGIN",
		1501: "GM_CMD_GC_OPERATE_HOOK_FISH",
		1599: "GM_CMD_GM_HOOK_END",
		1600: "GC_MSG_BEGIN",
		1601: "GC_MSG_SEND_MAIL",
		1610: "GC_MSG_BROADCAST",
		1611: "GC_MSG_ANN_POP_GET",
		1612: "GC_MSG_ANN_POP_EDIT",
		1613: "GC_MSG_ANN_POP_DEL",
		1699: "GC_MSG_END",
		1700: "GC_USER_BEGIN",
		1701: "GC_USER_BATCH_PLAYER_INFO",
		1799: "GC_USER_END",
		1800: "GC_RANK_BEGIN",
		1801: "GC_RANK_FLUSH_RANK",
		1802: "GC_RANK_REWARD_RANK",
		1899: "GC_RANK_END",
	}
	GM_CMD_value = map[string]int32{
		"GC_UNKNOWN":                  0,
		"GC_GAMETIME":                 1,
		"GC_LOAD_RIG_RULE":            2,
		"GC_DOWNTIME_KICK":            3,
		"GC_WORLD_BEGIN":              1000,
		"GC_CLEAR_WEATHER":            1001,
		"GC_WORLD_END":                1099,
		"GC_HALL_BEGIN":               1100,
		"GC_HALL_ITEM":                1101,
		"GC_HALL_CONTINUOUS_LOGIN":    1102,
		"GC_HALL_CREATE_CDK":          1103,
		"GC_HALL_QUERY_CDK_BATCHES":   1104,
		"GC_HALL_QUERY_CDK_RECORDS":   1105,
		"GC_HALL_DISABLE_CDK_BATCHES": 1106,
		"GC_HALL_END":                 1199,
		"GC_ASSET_BEGIN":              1200,
		"GC_ASSET_CLEAR_CATEGORY":     1201,
		"GC_ASSET_END":                1299,
		"GM_TASK_BEGIN":               1300,
		"GM_TASK_OPERATE":             1301,
		"GM_TASK_PROGRESS_SET":        1302,
		"GM_TASK_SUB_UPDATE":          1303,
		"GM_TASK_END":                 1399,
		"GM_CMD_GM_SPOT_BEGIN":        1400,
		"GM_CMD_GC_MODIFY_ENERGY":     1401,
		"GM_CMD_GM_SPOT_END":          1499,
		"GM_CMD_GM_HOOK_BEGIN":        1500,
		"GM_CMD_GC_OPERATE_HOOK_FISH": 1501,
		"GM_CMD_GM_HOOK_END":          1599,
		"GC_MSG_BEGIN":                1600,
		"GC_MSG_SEND_MAIL":            1601,
		"GC_MSG_BROADCAST":            1610,
		"GC_MSG_ANN_POP_GET":          1611,
		"GC_MSG_ANN_POP_EDIT":         1612,
		"GC_MSG_ANN_POP_DEL":          1613,
		"GC_MSG_END":                  1699,
		"GC_USER_BEGIN":               1700,
		"GC_USER_BATCH_PLAYER_INFO":   1701,
		"GC_USER_END":                 1799,
		"GC_RANK_BEGIN":               1800,
		"GC_RANK_FLUSH_RANK":          1801,
		"GC_RANK_REWARD_RANK":         1802,
		"GC_RANK_END":                 1899,
	}
)

func (x GM_CMD) Enum() *GM_CMD {
	p := new(GM_CMD)
	*p = x
	return p
}

func (x GM_CMD) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GM_CMD) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[80].Descriptor()
}

func (GM_CMD) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[80]
}

func (x GM_CMD) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GM_CMD.Descriptor instead.
func (GM_CMD) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{80}
}

// ************************************************************************
type EVENT_TYPE int32

const (
	EVENT_TYPE_ET_UNKNOWN            EVENT_TYPE = 0
	EVENT_TYPE_ET_ROLE_LEVEL_UP      EVENT_TYPE = 1  // 角色升级
	EVENT_TYPE_ET_ITEM_CHANGE        EVENT_TYPE = 10 // 道具变更
	EVENT_TYPE_ET_ITEM_ADD           EVENT_TYPE = 11 // 道具增加
	EVENT_TYPE_ET_ITEM_REDUCE        EVENT_TYPE = 12 // 道具消耗
	EVENT_TYPE_ET_FISH_GET           EVENT_TYPE = 30 // 中鱼
	EVENT_TYPE_ET_FISH_GET_WEIGHT    EVENT_TYPE = 31 // 中鱼-重量 (非标准事件-拓展处理)
	EVENT_TYPE_ET_LOGIN              EVENT_TYPE = 40 // 用户登录事件
	EVENT_TYPE_ET_LOGOUT             EVENT_TYPE = 41 // 用户登出
	EVENT_TYPE_ET_TASK_COMPLETE      EVENT_TYPE = 50 // 任务完成
	EVENT_TYPE_ET_ENTER_SPOT         EVENT_TYPE = 60 // 进入钓点
	EVENT_TYPE_ET_LEAVE_SPOT         EVENT_TYPE = 61 // 离开钓点
	EVENT_TYPE_ET_TRIP_SETTLE        EVENT_TYPE = 62 // 旅行结算
	EVENT_TYPE_ET_TRIP_SETTLE_WEIGHT EVENT_TYPE = 63 // 结算时最大的鱼 (非标准事件)
	EVENT_TYPE_ET_TRIP_SETTLE_VAL    EVENT_TYPE = 64 // 结算时最有价值的鱼 (非标准事件)
	EVENT_TYPE_ET_FISH_KEEPNET       EVENT_TYPE = 65 // 鱼入户
)

// Enum value maps for EVENT_TYPE.
var (
	EVENT_TYPE_name = map[int32]string{
		0:  "ET_UNKNOWN",
		1:  "ET_ROLE_LEVEL_UP",
		10: "ET_ITEM_CHANGE",
		11: "ET_ITEM_ADD",
		12: "ET_ITEM_REDUCE",
		30: "ET_FISH_GET",
		31: "ET_FISH_GET_WEIGHT",
		40: "ET_LOGIN",
		41: "ET_LOGOUT",
		50: "ET_TASK_COMPLETE",
		60: "ET_ENTER_SPOT",
		61: "ET_LEAVE_SPOT",
		62: "ET_TRIP_SETTLE",
		63: "ET_TRIP_SETTLE_WEIGHT",
		64: "ET_TRIP_SETTLE_VAL",
		65: "ET_FISH_KEEPNET",
	}
	EVENT_TYPE_value = map[string]int32{
		"ET_UNKNOWN":            0,
		"ET_ROLE_LEVEL_UP":      1,
		"ET_ITEM_CHANGE":        10,
		"ET_ITEM_ADD":           11,
		"ET_ITEM_REDUCE":        12,
		"ET_FISH_GET":           30,
		"ET_FISH_GET_WEIGHT":    31,
		"ET_LOGIN":              40,
		"ET_LOGOUT":             41,
		"ET_TASK_COMPLETE":      50,
		"ET_ENTER_SPOT":         60,
		"ET_LEAVE_SPOT":         61,
		"ET_TRIP_SETTLE":        62,
		"ET_TRIP_SETTLE_WEIGHT": 63,
		"ET_TRIP_SETTLE_VAL":    64,
		"ET_FISH_KEEPNET":       65,
	}
)

func (x EVENT_TYPE) Enum() *EVENT_TYPE {
	p := new(EVENT_TYPE)
	*p = x
	return p
}

func (x EVENT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EVENT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[81].Descriptor()
}

func (EVENT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[81]
}

func (x EVENT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EVENT_TYPE.Descriptor instead.
func (EVENT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{81}
}

// int 事件子字段埋点 mainKey * 1000
// 与下面 EVENT_STR_KEY 中枚举值不能重复
type EVENT_INT_KEY int32

const (
	EVENT_INT_KEY_EIK_UNKNOWN           EVENT_INT_KEY = 0
	EVENT_INT_KEY_EIK_COMMON_TS         EVENT_INT_KEY = 1 // 通用-时间戳
	EVENT_INT_KEY_EIK_ROLE_LEVEL        EVENT_INT_KEY = 2 // 角色-等级
	EVENT_INT_KEY_EIK_ROLE_BEFORE_LEVEL EVENT_INT_KEY = 3 // 角色-升级前等级
	// 道具相关 1001-2000
	EVENT_INT_KEY_EIK_ITEM_ID       EVENT_INT_KEY = 1001 // 道具-id
	EVENT_INT_KEY_EIK_ITEM_CATEGORY EVENT_INT_KEY = 1002 // 道具-种类
	EVENT_INT_KEY_EIK_ITEM_TYPE     EVENT_INT_KEY = 1003 // 道具-类型
	EVENT_INT_KEY_EIK_ITEM_SOURCE   EVENT_INT_KEY = 1004 // 道具-来源
	EVENT_INT_KEY_EIK_ITEM_COUNT    EVENT_INT_KEY = 1005 // 道具-数量
	// 中鱼相关 3001-4000
	EVENT_INT_KEY_EIK_FISH_POND              EVENT_INT_KEY = 3001 // 中鱼-钓场
	EVENT_INT_KEY_EIK_FISH_WEATHER           EVENT_INT_KEY = 3002 // 中鱼-天气 (未实现)
	EVENT_INT_KEY_EIK_FISH_ID                EVENT_INT_KEY = 3003 // 中鱼-鱼id
	EVENT_INT_KEY_EIK_FISH_WEIGHT            EVENT_INT_KEY = 3004 // 中鱼-鱼重
	EVENT_INT_KEY_EIK_FISH_ROG               EVENT_INT_KEY = 3005 // 中鱼-钓竿 (未实现)
	EVENT_INT_KEY_EIK_FISH_LENGTH            EVENT_INT_KEY = 3006 // 中鱼-鱼长
	EVENT_INT_KEY_EIK_FISH_QUALITY           EVENT_INT_KEY = 3007 // 中鱼-鱼品质 (未确定)
	EVENT_INT_KEY_EIK_FISH_GENUS             EVENT_INT_KEY = 3008 // 中鱼-属科
	EVENT_INT_KEY_EIK_FISH_SOMATOTYPE        EVENT_INT_KEY = 3009 // 中鱼-体型
	EVENT_INT_KEY_EIK_FISH_SPECIAL           EVENT_INT_KEY = 3010 // 中鱼-品种
	EVENT_INT_KEY_EIK_FISH_BAIT              EVENT_INT_KEY = 3011 // 中鱼-钓饵
	EVENT_INT_KEY_EIK_FISH_BROKEN            EVENT_INT_KEY = 3012 // 中鱼-鱼破损
	EVENT_INT_KEY_EIK_FISH_CONGRATULATION_ID EVENT_INT_KEY = 3013 // 中鱼-钓鱼日志祝贺词ID
	EVENT_INT_KEY_EIK_FISH_NUM               EVENT_INT_KEY = 3014 // 中鱼-鱼数量
	// 登录相关 4001-5000
	EVENT_INT_KEY_EIK_LOGIN_DATE         EVENT_INT_KEY = 4002 // 登入-日期
	EVENT_INT_KEY_EIK_LOGIN_MODEL        EVENT_INT_KEY = 4003 // 登入-方式
	EVENT_INT_KEY_EIK_LOGOUT_ONLINE_TIME EVENT_INT_KEY = 4101 // 登出-在线时长
	EVENT_INT_KEY_EIK_LOGOUT_MODEL       EVENT_INT_KEY = 4102 // 登出-方式
	// 钓场相关 5001-6000
	EVENT_INT_KEY_EIK_POND_ID           EVENT_INT_KEY = 5002 // 钓场 - 钓场id
	EVENT_INT_KEY_EIK_SPOT_ID           EVENT_INT_KEY = 5003 // 钓场 - 钓点id
	EVENT_INT_KEY_EIK_TRIP_COINS_RECORD EVENT_INT_KEY = 5004 // 结算 - 金币记录
	EVENT_INT_KEY_EIK_TRIP_FISH_VAL     EVENT_INT_KEY = 5005 // 结算 - 渔获价值
	// 任务相关 6001-7000
	EVENT_INT_KEY_EIK_TASK_ID       EVENT_INT_KEY = 6001 // 任务-id
	EVENT_INT_KEY_EIK_TASK_TYPE     EVENT_INT_KEY = 6002 // 任务-类型
	EVENT_INT_KEY_EIK_TASK_SUB_TYPE EVENT_INT_KEY = 6003 // 任务-子任务类型
)

// Enum value maps for EVENT_INT_KEY.
var (
	EVENT_INT_KEY_name = map[int32]string{
		0:    "EIK_UNKNOWN",
		1:    "EIK_COMMON_TS",
		2:    "EIK_ROLE_LEVEL",
		3:    "EIK_ROLE_BEFORE_LEVEL",
		1001: "EIK_ITEM_ID",
		1002: "EIK_ITEM_CATEGORY",
		1003: "EIK_ITEM_TYPE",
		1004: "EIK_ITEM_SOURCE",
		1005: "EIK_ITEM_COUNT",
		3001: "EIK_FISH_POND",
		3002: "EIK_FISH_WEATHER",
		3003: "EIK_FISH_ID",
		3004: "EIK_FISH_WEIGHT",
		3005: "EIK_FISH_ROG",
		3006: "EIK_FISH_LENGTH",
		3007: "EIK_FISH_QUALITY",
		3008: "EIK_FISH_GENUS",
		3009: "EIK_FISH_SOMATOTYPE",
		3010: "EIK_FISH_SPECIAL",
		3011: "EIK_FISH_BAIT",
		3012: "EIK_FISH_BROKEN",
		3013: "EIK_FISH_CONGRATULATION_ID",
		3014: "EIK_FISH_NUM",
		4002: "EIK_LOGIN_DATE",
		4003: "EIK_LOGIN_MODEL",
		4101: "EIK_LOGOUT_ONLINE_TIME",
		4102: "EIK_LOGOUT_MODEL",
		5002: "EIK_POND_ID",
		5003: "EIK_SPOT_ID",
		5004: "EIK_TRIP_COINS_RECORD",
		5005: "EIK_TRIP_FISH_VAL",
		6001: "EIK_TASK_ID",
		6002: "EIK_TASK_TYPE",
		6003: "EIK_TASK_SUB_TYPE",
	}
	EVENT_INT_KEY_value = map[string]int32{
		"EIK_UNKNOWN":                0,
		"EIK_COMMON_TS":              1,
		"EIK_ROLE_LEVEL":             2,
		"EIK_ROLE_BEFORE_LEVEL":      3,
		"EIK_ITEM_ID":                1001,
		"EIK_ITEM_CATEGORY":          1002,
		"EIK_ITEM_TYPE":              1003,
		"EIK_ITEM_SOURCE":            1004,
		"EIK_ITEM_COUNT":             1005,
		"EIK_FISH_POND":              3001,
		"EIK_FISH_WEATHER":           3002,
		"EIK_FISH_ID":                3003,
		"EIK_FISH_WEIGHT":            3004,
		"EIK_FISH_ROG":               3005,
		"EIK_FISH_LENGTH":            3006,
		"EIK_FISH_QUALITY":           3007,
		"EIK_FISH_GENUS":             3008,
		"EIK_FISH_SOMATOTYPE":        3009,
		"EIK_FISH_SPECIAL":           3010,
		"EIK_FISH_BAIT":              3011,
		"EIK_FISH_BROKEN":            3012,
		"EIK_FISH_CONGRATULATION_ID": 3013,
		"EIK_FISH_NUM":               3014,
		"EIK_LOGIN_DATE":             4002,
		"EIK_LOGIN_MODEL":            4003,
		"EIK_LOGOUT_ONLINE_TIME":     4101,
		"EIK_LOGOUT_MODEL":           4102,
		"EIK_POND_ID":                5002,
		"EIK_SPOT_ID":                5003,
		"EIK_TRIP_COINS_RECORD":      5004,
		"EIK_TRIP_FISH_VAL":          5005,
		"EIK_TASK_ID":                6001,
		"EIK_TASK_TYPE":              6002,
		"EIK_TASK_SUB_TYPE":          6003,
	}
)

func (x EVENT_INT_KEY) Enum() *EVENT_INT_KEY {
	p := new(EVENT_INT_KEY)
	*p = x
	return p
}

func (x EVENT_INT_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EVENT_INT_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[82].Descriptor()
}

func (EVENT_INT_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[82]
}

func (x EVENT_INT_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EVENT_INT_KEY.Descriptor instead.
func (EVENT_INT_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{82}
}

// string 事件子字段埋点 mainKey * 1000
type EVENT_STR_KEY int32

const (
	EVENT_STR_KEY_ESK_UNKNOWN EVENT_STR_KEY = 0
	// 登录相关 4001-5000
	EVENT_STR_KEY_ESK_LOGOUT_RECENT_MSG_IDS EVENT_STR_KEY = 4103 // 登出-最近消息id列表
	// 钓场相关 5001-6000
	EVENT_STR_KEY_ESK_ROOM_ID EVENT_STR_KEY = 5001 // 钓场 - 房间id
)

// Enum value maps for EVENT_STR_KEY.
var (
	EVENT_STR_KEY_name = map[int32]string{
		0:    "ESK_UNKNOWN",
		4103: "ESK_LOGOUT_RECENT_MSG_IDS",
		5001: "ESK_ROOM_ID",
	}
	EVENT_STR_KEY_value = map[string]int32{
		"ESK_UNKNOWN":               0,
		"ESK_LOGOUT_RECENT_MSG_IDS": 4103,
		"ESK_ROOM_ID":               5001,
	}
)

func (x EVENT_STR_KEY) Enum() *EVENT_STR_KEY {
	p := new(EVENT_STR_KEY)
	*p = x
	return p
}

func (x EVENT_STR_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EVENT_STR_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[83].Descriptor()
}

func (EVENT_STR_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[83]
}

func (x EVENT_STR_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EVENT_STR_KEY.Descriptor instead.
func (EVENT_STR_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{83}
}

type EVENT_UNIQUE_KEY int32

const (
	EVENT_UNIQUE_KEY_EUK_UNKNOWN           EVENT_UNIQUE_KEY = 0
	EVENT_UNIQUE_KEY_EUK_TRIP_COINS_RECORD EVENT_UNIQUE_KEY = 20141088 // 旅行结算金币记录
)

// Enum value maps for EVENT_UNIQUE_KEY.
var (
	EVENT_UNIQUE_KEY_name = map[int32]string{
		0:        "EUK_UNKNOWN",
		20141088: "EUK_TRIP_COINS_RECORD",
	}
	EVENT_UNIQUE_KEY_value = map[string]int32{
		"EUK_UNKNOWN":           0,
		"EUK_TRIP_COINS_RECORD": 20141088,
	}
)

func (x EVENT_UNIQUE_KEY) Enum() *EVENT_UNIQUE_KEY {
	p := new(EVENT_UNIQUE_KEY)
	*p = x
	return p
}

func (x EVENT_UNIQUE_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EVENT_UNIQUE_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[84].Descriptor()
}

func (EVENT_UNIQUE_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[84]
}

func (x EVENT_UNIQUE_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EVENT_UNIQUE_KEY.Descriptor instead.
func (EVENT_UNIQUE_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{84}
}

// 统计类型
type STATS_TYPE int32

const (
	STATS_TYPE_STATS_TYPE_UNKNOWN STATS_TYPE = 0 // 重复了
	STATS_TYPE_ST_NORMAL          STATS_TYPE = 1 // 通用统计
	STATS_TYPE_ST_FISH            STATS_TYPE = 2 // 鱼(品种)统计
	STATS_TYPE_ST_SETTLE_RECORD   STATS_TYPE = 3 // 结算记录统计
	STATS_TYPE_ST_PLAY_TIME       STATS_TYPE = 4 // 游玩时间统计
	STATS_TYPE_ST_TROPHY          STATS_TYPE = 5 // 奖杯统计
	STATS_TYPE_ST_FISH_ID         STATS_TYPE = 6 // 鱼(id)统计
)

// Enum value maps for STATS_TYPE.
var (
	STATS_TYPE_name = map[int32]string{
		0: "STATS_TYPE_UNKNOWN",
		1: "ST_NORMAL",
		2: "ST_FISH",
		3: "ST_SETTLE_RECORD",
		4: "ST_PLAY_TIME",
		5: "ST_TROPHY",
		6: "ST_FISH_ID",
	}
	STATS_TYPE_value = map[string]int32{
		"STATS_TYPE_UNKNOWN": 0,
		"ST_NORMAL":          1,
		"ST_FISH":            2,
		"ST_SETTLE_RECORD":   3,
		"ST_PLAY_TIME":       4,
		"ST_TROPHY":          5,
		"ST_FISH_ID":         6,
	}
)

func (x STATS_TYPE) Enum() *STATS_TYPE {
	p := new(STATS_TYPE)
	*p = x
	return p
}

func (x STATS_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (STATS_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[85].Descriptor()
}

func (STATS_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[85]
}

func (x STATS_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use STATS_TYPE.Descriptor instead.
func (STATS_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{85}
}

// 辅助统计类型
type StatsSubType int32

const (
	StatsSubType_STS_UNKNOWN         StatsSubType = 0
	StatsSubType_STS_FISH_WEIGHT_MAX StatsSubType = 1 // 鱼-最大重量
	StatsSubType_STS_FISH_COUNT      StatsSubType = 2 // 鱼-数量
	StatsSubType_STS_FISH_BEST_SIZE  StatsSubType = 3 // 鱼-最好的尺寸
)

// Enum value maps for StatsSubType.
var (
	StatsSubType_name = map[int32]string{
		0: "STS_UNKNOWN",
		1: "STS_FISH_WEIGHT_MAX",
		2: "STS_FISH_COUNT",
		3: "STS_FISH_BEST_SIZE",
	}
	StatsSubType_value = map[string]int32{
		"STS_UNKNOWN":         0,
		"STS_FISH_WEIGHT_MAX": 1,
		"STS_FISH_COUNT":      2,
		"STS_FISH_BEST_SIZE":  3,
	}
)

func (x StatsSubType) Enum() *StatsSubType {
	p := new(StatsSubType)
	*p = x
	return p
}

func (x StatsSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatsSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[86].Descriptor()
}

func (StatsSubType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[86]
}

func (x StatsSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatsSubType.Descriptor instead.
func (StatsSubType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{86}
}

// 任务状态
type TASK_STATUS int32

const (
	TASK_STATUS_TS_UNKNOWN      TASK_STATUS = 0
	TASK_STATUS_TS_UNACCEPTABLE TASK_STATUS = 1 // 未接受
	TASK_STATUS_TS_ACCEPTABLE   TASK_STATUS = 2 // 可接受
	TASK_STATUS_TS_DURING       TASK_STATUS = 3 // 进行中
	TASK_STATUS_TS_SUBMIT       TASK_STATUS = 4 // 已提交
	TASK_STATUS_TS_COMPLETE     TASK_STATUS = 5 // 已完成
	TASK_STATUS_TS_DELETE       TASK_STATUS = 6 // 删除
)

// Enum value maps for TASK_STATUS.
var (
	TASK_STATUS_name = map[int32]string{
		0: "TS_UNKNOWN",
		1: "TS_UNACCEPTABLE",
		2: "TS_ACCEPTABLE",
		3: "TS_DURING",
		4: "TS_SUBMIT",
		5: "TS_COMPLETE",
		6: "TS_DELETE",
	}
	TASK_STATUS_value = map[string]int32{
		"TS_UNKNOWN":      0,
		"TS_UNACCEPTABLE": 1,
		"TS_ACCEPTABLE":   2,
		"TS_DURING":       3,
		"TS_SUBMIT":       4,
		"TS_COMPLETE":     5,
		"TS_DELETE":       6,
	}
)

func (x TASK_STATUS) Enum() *TASK_STATUS {
	p := new(TASK_STATUS)
	*p = x
	return p
}

func (x TASK_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TASK_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[87].Descriptor()
}

func (TASK_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[87]
}

func (x TASK_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TASK_STATUS.Descriptor instead.
func (TASK_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{87}
}

type TASK_ADD_TYPE int32

const (
	TASK_ADD_TYPE_TAT_UNKNOWN TASK_ADD_TYPE = 0
	TASK_ADD_TYPE_TAT_ADD     TASK_ADD_TYPE = 1 /// 加类型
	TASK_ADD_TYPE_TAT_VAL     TASK_ADD_TYPE = 2 // 值类型
)

// Enum value maps for TASK_ADD_TYPE.
var (
	TASK_ADD_TYPE_name = map[int32]string{
		0: "TAT_UNKNOWN",
		1: "TAT_ADD",
		2: "TAT_VAL",
	}
	TASK_ADD_TYPE_value = map[string]int32{
		"TAT_UNKNOWN": 0,
		"TAT_ADD":     1,
		"TAT_VAL":     2,
	}
)

func (x TASK_ADD_TYPE) Enum() *TASK_ADD_TYPE {
	p := new(TASK_ADD_TYPE)
	*p = x
	return p
}

func (x TASK_ADD_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TASK_ADD_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[88].Descriptor()
}

func (TASK_ADD_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[88]
}

func (x TASK_ADD_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TASK_ADD_TYPE.Descriptor instead.
func (TASK_ADD_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{88}
}

type TASK_CATEGORY int32

const (
	TASK_CATEGORY_TC_UNKNOWN    TASK_CATEGORY = 0
	TASK_CATEGORY_TC_MAIN       TASK_CATEGORY = 1 // 主线任务
	TASK_CATEGORY_TC_POND       TASK_CATEGORY = 2 // 场景探索任务
	TASK_CATEGORY_TC_DAILY      TASK_CATEGORY = 3 // 每日任务
	TASK_CATEGORY_TC_ACHIEVE    TASK_CATEGORY = 4 // 成就任务
	TASK_CATEGORY_TcFishachieve TASK_CATEGORY = 6 // 图鉴任务
)

// Enum value maps for TASK_CATEGORY.
var (
	TASK_CATEGORY_name = map[int32]string{
		0: "TC_UNKNOWN",
		1: "TC_MAIN",
		2: "TC_POND",
		3: "TC_DAILY",
		4: "TC_ACHIEVE",
		6: "TcFishachieve",
	}
	TASK_CATEGORY_value = map[string]int32{
		"TC_UNKNOWN":    0,
		"TC_MAIN":       1,
		"TC_POND":       2,
		"TC_DAILY":      3,
		"TC_ACHIEVE":    4,
		"TcFishachieve": 6,
	}
)

func (x TASK_CATEGORY) Enum() *TASK_CATEGORY {
	p := new(TASK_CATEGORY)
	*p = x
	return p
}

func (x TASK_CATEGORY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TASK_CATEGORY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[89].Descriptor()
}

func (TASK_CATEGORY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[89]
}

func (x TASK_CATEGORY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TASK_CATEGORY.Descriptor instead.
func (TASK_CATEGORY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{89}
}

// 进度积分系统
type PROGRESS_TYPE int32

const (
	PROGRESS_TYPE_PXT_UNKNOWN      PROGRESS_TYPE = 0
	PROGRESS_TYPE_PXT_ENCYCLOPEDIA PROGRESS_TYPE = 1 // 百科
	PROGRESS_TYPE_PXT_HANDBOOK     PROGRESS_TYPE = 2 // 图鉴
)

// Enum value maps for PROGRESS_TYPE.
var (
	PROGRESS_TYPE_name = map[int32]string{
		0: "PXT_UNKNOWN",
		1: "PXT_ENCYCLOPEDIA",
		2: "PXT_HANDBOOK",
	}
	PROGRESS_TYPE_value = map[string]int32{
		"PXT_UNKNOWN":      0,
		"PXT_ENCYCLOPEDIA": 1,
		"PXT_HANDBOOK":     2,
	}
)

func (x PROGRESS_TYPE) Enum() *PROGRESS_TYPE {
	p := new(PROGRESS_TYPE)
	*p = x
	return p
}

func (x PROGRESS_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PROGRESS_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[90].Descriptor()
}

func (PROGRESS_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[90]
}

func (x PROGRESS_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PROGRESS_TYPE.Descriptor instead.
func (PROGRESS_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{90}
}

// 表达式 是否
type EXPRESSION_TYPE int32

const (
	EXPRESSION_TYPE_ET_NO  EXPRESSION_TYPE = 0 // 否
	EXPRESSION_TYPE_ET_YES EXPRESSION_TYPE = 1 // 是
)

// Enum value maps for EXPRESSION_TYPE.
var (
	EXPRESSION_TYPE_name = map[int32]string{
		0: "ET_NO",
		1: "ET_YES",
	}
	EXPRESSION_TYPE_value = map[string]int32{
		"ET_NO":  0,
		"ET_YES": 1,
	}
)

func (x EXPRESSION_TYPE) Enum() *EXPRESSION_TYPE {
	p := new(EXPRESSION_TYPE)
	*p = x
	return p
}

func (x EXPRESSION_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EXPRESSION_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[91].Descriptor()
}

func (EXPRESSION_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[91]
}

func (x EXPRESSION_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EXPRESSION_TYPE.Descriptor instead.
func (EXPRESSION_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{91}
}

type VAL_OPERATE int32

const (
	VAL_OPERATE_VO_UNKNOWN       VAL_OPERATE = 0 // 未知
	VAL_OPERATE_VO_LESS          VAL_OPERATE = 1 // 小于
	VAL_OPERATE_VO_LESS_EQUAL    VAL_OPERATE = 2 // 小于等于
	VAL_OPERATE_VO_EQUAL         VAL_OPERATE = 3 // 等于
	VAL_OPERATE_VO_GREATER       VAL_OPERATE = 4 // 大于
	VAL_OPERATE_VO_GREATER_EQUAL VAL_OPERATE = 5 // 大于等于
	VAL_OPERATE_VO_NOT_EQUAL     VAL_OPERATE = 6 // 不等于
)

// Enum value maps for VAL_OPERATE.
var (
	VAL_OPERATE_name = map[int32]string{
		0: "VO_UNKNOWN",
		1: "VO_LESS",
		2: "VO_LESS_EQUAL",
		3: "VO_EQUAL",
		4: "VO_GREATER",
		5: "VO_GREATER_EQUAL",
		6: "VO_NOT_EQUAL",
	}
	VAL_OPERATE_value = map[string]int32{
		"VO_UNKNOWN":       0,
		"VO_LESS":          1,
		"VO_LESS_EQUAL":    2,
		"VO_EQUAL":         3,
		"VO_GREATER":       4,
		"VO_GREATER_EQUAL": 5,
		"VO_NOT_EQUAL":     6,
	}
)

func (x VAL_OPERATE) Enum() *VAL_OPERATE {
	p := new(VAL_OPERATE)
	*p = x
	return p
}

func (x VAL_OPERATE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VAL_OPERATE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[92].Descriptor()
}

func (VAL_OPERATE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[92]
}

func (x VAL_OPERATE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VAL_OPERATE.Descriptor instead.
func (VAL_OPERATE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{92}
}

type AOI int32

const (
	AOI_AOI_UNKNOWN AOI = 0
	AOI_AOI_AND     AOI = 1 // 与
	AOI_AOI_OR      AOI = 2 // 或
	AOI_AOI_INVERT  AOI = 3 // 非
)

// Enum value maps for AOI.
var (
	AOI_name = map[int32]string{
		0: "AOI_UNKNOWN",
		1: "AOI_AND",
		2: "AOI_OR",
		3: "AOI_INVERT",
	}
	AOI_value = map[string]int32{
		"AOI_UNKNOWN": 0,
		"AOI_AND":     1,
		"AOI_OR":      2,
		"AOI_INVERT":  3,
	}
)

func (x AOI) Enum() *AOI {
	p := new(AOI)
	*p = x
	return p
}

func (x AOI) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AOI) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[93].Descriptor()
}

func (AOI) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[93]
}

func (x AOI) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AOI.Descriptor instead.
func (AOI) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{93}
}

type TASK_OPEN int32

const (
	TASK_OPEN_TO_UNKNOW        TASK_OPEN = 0
	TASK_OPEN_TO_ROLE_LEV_GT   TASK_OPEN = 1 // 角色等级大于等于
	TASK_OPEN_TO_TASK_COMPLETE TASK_OPEN = 2 // 任务完成
)

// Enum value maps for TASK_OPEN.
var (
	TASK_OPEN_name = map[int32]string{
		0: "TO_UNKNOW",
		1: "TO_ROLE_LEV_GT",
		2: "TO_TASK_COMPLETE",
	}
	TASK_OPEN_value = map[string]int32{
		"TO_UNKNOW":        0,
		"TO_ROLE_LEV_GT":   1,
		"TO_TASK_COMPLETE": 2,
	}
)

func (x TASK_OPEN) Enum() *TASK_OPEN {
	p := new(TASK_OPEN)
	*p = x
	return p
}

func (x TASK_OPEN) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TASK_OPEN) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[94].Descriptor()
}

func (TASK_OPEN) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[94]
}

func (x TASK_OPEN) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TASK_OPEN.Descriptor instead.
func (TASK_OPEN) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{94}
}

type TASK_TRACE int32

const (
	TASK_TRACE_TT_UNKNOW     TASK_TRACE = 0 // 无
	TASK_TRACE_TT_TRACE_YES  TASK_TRACE = 1 // 追踪
	TASK_TRACE_TT_TRACE_AUTO TASK_TRACE = 2 // 自动追踪
)

// Enum value maps for TASK_TRACE.
var (
	TASK_TRACE_name = map[int32]string{
		0: "TT_UNKNOW",
		1: "TT_TRACE_YES",
		2: "TT_TRACE_AUTO",
	}
	TASK_TRACE_value = map[string]int32{
		"TT_UNKNOW":     0,
		"TT_TRACE_YES":  1,
		"TT_TRACE_AUTO": 2,
	}
)

func (x TASK_TRACE) Enum() *TASK_TRACE {
	p := new(TASK_TRACE)
	*p = x
	return p
}

func (x TASK_TRACE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TASK_TRACE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[95].Descriptor()
}

func (TASK_TRACE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[95]
}

func (x TASK_TRACE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TASK_TRACE.Descriptor instead.
func (TASK_TRACE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{95}
}

// 累加类型
type SUM_ADD int32

const (
	SUM_ADD_SA_UNKNOWN      SUM_ADD = 0
	SUM_ADD_SA_ADD          SUM_ADD = 1 // +=
	SUM_ADD_SA_VAL          SUM_ADD = 2 // =
	SUM_ADD_SA_MAX          SUM_ADD = 3 // max(val,cur)
	SUM_ADD_SA_UNI_ADD      SUM_ADD = 4 // 去重累加
	SUM_ADD_SA_MIN          SUM_ADD = 5 // 最小值
	SUM_ADD_SA_CONTINUE_DAY SUM_ADD = 6 // 连续日期
)

// Enum value maps for SUM_ADD.
var (
	SUM_ADD_name = map[int32]string{
		0: "SA_UNKNOWN",
		1: "SA_ADD",
		2: "SA_VAL",
		3: "SA_MAX",
		4: "SA_UNI_ADD",
		5: "SA_MIN",
		6: "SA_CONTINUE_DAY",
	}
	SUM_ADD_value = map[string]int32{
		"SA_UNKNOWN":      0,
		"SA_ADD":          1,
		"SA_VAL":          2,
		"SA_MAX":          3,
		"SA_UNI_ADD":      4,
		"SA_MIN":          5,
		"SA_CONTINUE_DAY": 6,
	}
)

func (x SUM_ADD) Enum() *SUM_ADD {
	p := new(SUM_ADD)
	*p = x
	return p
}

func (x SUM_ADD) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SUM_ADD) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[96].Descriptor()
}

func (SUM_ADD) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[96]
}

func (x SUM_ADD) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SUM_ADD.Descriptor instead.
func (SUM_ADD) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{96}
}

// 假期类型(数脉)
type HOLIDAY_TYPE int32

const (
	HOLIDAY_TYPE_HT_UNKNOWN HOLIDAY_TYPE = 0
	HOLIDAY_TYPE_HT_WORKING HOLIDAY_TYPE = 1 // 工作日
	HOLIDAY_TYPE_HT_WEEKEND HOLIDAY_TYPE = 2 // 周末
	HOLIDAY_TYPE_HT_HOLIDAY HOLIDAY_TYPE = 3 // 假期
)

// Enum value maps for HOLIDAY_TYPE.
var (
	HOLIDAY_TYPE_name = map[int32]string{
		0: "HT_UNKNOWN",
		1: "HT_WORKING",
		2: "HT_WEEKEND",
		3: "HT_HOLIDAY",
	}
	HOLIDAY_TYPE_value = map[string]int32{
		"HT_UNKNOWN": 0,
		"HT_WORKING": 1,
		"HT_WEEKEND": 2,
		"HT_HOLIDAY": 3,
	}
)

func (x HOLIDAY_TYPE) Enum() *HOLIDAY_TYPE {
	p := new(HOLIDAY_TYPE)
	*p = x
	return p
}

func (x HOLIDAY_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HOLIDAY_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[97].Descriptor()
}

func (HOLIDAY_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[97]
}

func (x HOLIDAY_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HOLIDAY_TYPE.Descriptor instead.
func (HOLIDAY_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{97}
}

type MSG_CATEGORY int32

const (
	MSG_CATEGORY_MC_UNKNOWN MSG_CATEGORY = 0
	MSG_CATEGORY_MC_MAIL    MSG_CATEGORY = 1 // 邮件
	MSG_CATEGORY_MC_NOTIFY  MSG_CATEGORY = 2 // 通知
)

// Enum value maps for MSG_CATEGORY.
var (
	MSG_CATEGORY_name = map[int32]string{
		0: "MC_UNKNOWN",
		1: "MC_MAIL",
		2: "MC_NOTIFY",
	}
	MSG_CATEGORY_value = map[string]int32{
		"MC_UNKNOWN": 0,
		"MC_MAIL":    1,
		"MC_NOTIFY":  2,
	}
)

func (x MSG_CATEGORY) Enum() *MSG_CATEGORY {
	p := new(MSG_CATEGORY)
	*p = x
	return p
}

func (x MSG_CATEGORY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MSG_CATEGORY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[98].Descriptor()
}

func (MSG_CATEGORY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[98]
}

func (x MSG_CATEGORY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MSG_CATEGORY.Descriptor instead.
func (MSG_CATEGORY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{98}
}

// 邮件状态
type MAIL_STATUS int32

const (
	MAIL_STATUS_MS_UNREAD  MAIL_STATUS = 0 // 未读
	MAIL_STATUS_MS_READ    MAIL_STATUS = 1 // 已读
	MAIL_STATUS_MS_EXPIRED MAIL_STATUS = 2 // 已过期
	MAIL_STATUS_MS_DELETE  MAIL_STATUS = 3 // 删除
)

// Enum value maps for MAIL_STATUS.
var (
	MAIL_STATUS_name = map[int32]string{
		0: "MS_UNREAD",
		1: "MS_READ",
		2: "MS_EXPIRED",
		3: "MS_DELETE",
	}
	MAIL_STATUS_value = map[string]int32{
		"MS_UNREAD":  0,
		"MS_READ":    1,
		"MS_EXPIRED": 2,
		"MS_DELETE":  3,
	}
)

func (x MAIL_STATUS) Enum() *MAIL_STATUS {
	p := new(MAIL_STATUS)
	*p = x
	return p
}

func (x MAIL_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[99].Descriptor()
}

func (MAIL_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[99]
}

func (x MAIL_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_STATUS.Descriptor instead.
func (MAIL_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{99}
}

type MAIL_ATTACH_STATUS int32

const (
	MAIL_ATTACH_STATUS_MAS_NOT_ATTACH MAIL_ATTACH_STATUS = 0 // 没有附件
	MAIL_ATTACH_STATUS_MAS_HAD_ATTACH MAIL_ATTACH_STATUS = 1 // 有附件
)

// Enum value maps for MAIL_ATTACH_STATUS.
var (
	MAIL_ATTACH_STATUS_name = map[int32]string{
		0: "MAS_NOT_ATTACH",
		1: "MAS_HAD_ATTACH",
	}
	MAIL_ATTACH_STATUS_value = map[string]int32{
		"MAS_NOT_ATTACH": 0,
		"MAS_HAD_ATTACH": 1,
	}
)

func (x MAIL_ATTACH_STATUS) Enum() *MAIL_ATTACH_STATUS {
	p := new(MAIL_ATTACH_STATUS)
	*p = x
	return p
}

func (x MAIL_ATTACH_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_ATTACH_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[100].Descriptor()
}

func (MAIL_ATTACH_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[100]
}

func (x MAIL_ATTACH_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_ATTACH_STATUS.Descriptor instead.
func (MAIL_ATTACH_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{100}
}

type MAIL_ATTACH_CLAIM_STATUS int32

const (
	MAIL_ATTACH_CLAIM_STATUS_MACS_NOT_CLAIM MAIL_ATTACH_CLAIM_STATUS = 0 // 未领取
	MAIL_ATTACH_CLAIM_STATUS_MACS_HAD_CLAIM MAIL_ATTACH_CLAIM_STATUS = 1 // 已领取
)

// Enum value maps for MAIL_ATTACH_CLAIM_STATUS.
var (
	MAIL_ATTACH_CLAIM_STATUS_name = map[int32]string{
		0: "MACS_NOT_CLAIM",
		1: "MACS_HAD_CLAIM",
	}
	MAIL_ATTACH_CLAIM_STATUS_value = map[string]int32{
		"MACS_NOT_CLAIM": 0,
		"MACS_HAD_CLAIM": 1,
	}
)

func (x MAIL_ATTACH_CLAIM_STATUS) Enum() *MAIL_ATTACH_CLAIM_STATUS {
	p := new(MAIL_ATTACH_CLAIM_STATUS)
	*p = x
	return p
}

func (x MAIL_ATTACH_CLAIM_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_ATTACH_CLAIM_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[101].Descriptor()
}

func (MAIL_ATTACH_CLAIM_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[101]
}

func (x MAIL_ATTACH_CLAIM_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_ATTACH_CLAIM_STATUS.Descriptor instead.
func (MAIL_ATTACH_CLAIM_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{101}
}

type MAIL_TYPE int32

const (
	MAIL_TYPE_MT_UNKNOWN  MAIL_TYPE = 0
	MAIL_TYPE_MT_ORDINARY MAIL_TYPE = 1 // 普通
	MAIL_TYPE_MT_SYSTEM   MAIL_TYPE = 2 // 系统
)

// Enum value maps for MAIL_TYPE.
var (
	MAIL_TYPE_name = map[int32]string{
		0: "MT_UNKNOWN",
		1: "MT_ORDINARY",
		2: "MT_SYSTEM",
	}
	MAIL_TYPE_value = map[string]int32{
		"MT_UNKNOWN":  0,
		"MT_ORDINARY": 1,
		"MT_SYSTEM":   2,
	}
)

func (x MAIL_TYPE) Enum() *MAIL_TYPE {
	p := new(MAIL_TYPE)
	*p = x
	return p
}

func (x MAIL_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[102].Descriptor()
}

func (MAIL_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[102]
}

func (x MAIL_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_TYPE.Descriptor instead.
func (MAIL_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{102}
}

// 邮件拓展参数key
type MAIL_EXTEND_KEY int32

const (
	MAIL_EXTEND_KEY_MEK_UNKNOWN     MAIL_EXTEND_KEY = 0
	MAIL_EXTEND_KEY_MEK_RANK_ID     MAIL_EXTEND_KEY = 1 // 排行榜id
	MAIL_EXTEND_KEY_MEK_RANK_SORT   MAIL_EXTEND_KEY = 2 // 排行榜排名
	MAIL_EXTEND_KEY_MEK_ACTIVITY_ID MAIL_EXTEND_KEY = 3 // 活动id
)

// Enum value maps for MAIL_EXTEND_KEY.
var (
	MAIL_EXTEND_KEY_name = map[int32]string{
		0: "MEK_UNKNOWN",
		1: "MEK_RANK_ID",
		2: "MEK_RANK_SORT",
		3: "MEK_ACTIVITY_ID",
	}
	MAIL_EXTEND_KEY_value = map[string]int32{
		"MEK_UNKNOWN":     0,
		"MEK_RANK_ID":     1,
		"MEK_RANK_SORT":   2,
		"MEK_ACTIVITY_ID": 3,
	}
)

func (x MAIL_EXTEND_KEY) Enum() *MAIL_EXTEND_KEY {
	p := new(MAIL_EXTEND_KEY)
	*p = x
	return p
}

func (x MAIL_EXTEND_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAIL_EXTEND_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[103].Descriptor()
}

func (MAIL_EXTEND_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[103]
}

func (x MAIL_EXTEND_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAIL_EXTEND_KEY.Descriptor instead.
func (MAIL_EXTEND_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{103}
}

// 播报类型
type MSG_BROADCAST_TYPE int32

const (
	MSG_BROADCAST_TYPE_MB_UNKNOWN   MSG_BROADCAST_TYPE = 0
	MSG_BROADCAST_TYPE_MB_BROADCAST MSG_BROADCAST_TYPE = 1 // 广播
	MSG_BROADCAST_TYPE_MB_TIPS      MSG_BROADCAST_TYPE = 2 // tips
	MSG_BROADCAST_TYPE_MB_POPUP     MSG_BROADCAST_TYPE = 3 // 弹窗
)

// Enum value maps for MSG_BROADCAST_TYPE.
var (
	MSG_BROADCAST_TYPE_name = map[int32]string{
		0: "MB_UNKNOWN",
		1: "MB_BROADCAST",
		2: "MB_TIPS",
		3: "MB_POPUP",
	}
	MSG_BROADCAST_TYPE_value = map[string]int32{
		"MB_UNKNOWN":   0,
		"MB_BROADCAST": 1,
		"MB_TIPS":      2,
		"MB_POPUP":     3,
	}
)

func (x MSG_BROADCAST_TYPE) Enum() *MSG_BROADCAST_TYPE {
	p := new(MSG_BROADCAST_TYPE)
	*p = x
	return p
}

func (x MSG_BROADCAST_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MSG_BROADCAST_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[104].Descriptor()
}

func (MSG_BROADCAST_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[104]
}

func (x MSG_BROADCAST_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MSG_BROADCAST_TYPE.Descriptor instead.
func (MSG_BROADCAST_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{104}
}

// 通知类型
type MSG_BROADCAST_NTF_TYPE int32

const (
	MSG_BROADCAST_NTF_TYPE_MBN_UNKNOWN    MSG_BROADCAST_NTF_TYPE = 0
	MSG_BROADCAST_NTF_TYPE_MBN_NORMAL     MSG_BROADCAST_NTF_TYPE = 1 // 默认
	MSG_BROADCAST_NTF_TYPE_MBN_FISH_MOUTH MSG_BROADCAST_NTF_TYPE = 2 // 鱼口
)

// Enum value maps for MSG_BROADCAST_NTF_TYPE.
var (
	MSG_BROADCAST_NTF_TYPE_name = map[int32]string{
		0: "MBN_UNKNOWN",
		1: "MBN_NORMAL",
		2: "MBN_FISH_MOUTH",
	}
	MSG_BROADCAST_NTF_TYPE_value = map[string]int32{
		"MBN_UNKNOWN":    0,
		"MBN_NORMAL":     1,
		"MBN_FISH_MOUTH": 2,
	}
)

func (x MSG_BROADCAST_NTF_TYPE) Enum() *MSG_BROADCAST_NTF_TYPE {
	p := new(MSG_BROADCAST_NTF_TYPE)
	*p = x
	return p
}

func (x MSG_BROADCAST_NTF_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MSG_BROADCAST_NTF_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[105].Descriptor()
}

func (MSG_BROADCAST_NTF_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[105]
}

func (x MSG_BROADCAST_NTF_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MSG_BROADCAST_NTF_TYPE.Descriptor instead.
func (MSG_BROADCAST_NTF_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{105}
}

// 事件
type MSG_BROADCAST_EVENT int32

const (
	MSG_BROADCAST_EVENT_MBE_UNKNOWN    MSG_BROADCAST_EVENT = 0
	MSG_BROADCAST_EVENT_MBE_CATCH_FISH MSG_BROADCAST_EVENT = 1 // 中鱼
	MSG_BROADCAST_EVENT_MBE_FISH_MOUTH MSG_BROADCAST_EVENT = 2 // 鱼口
)

// Enum value maps for MSG_BROADCAST_EVENT.
var (
	MSG_BROADCAST_EVENT_name = map[int32]string{
		0: "MBE_UNKNOWN",
		1: "MBE_CATCH_FISH",
		2: "MBE_FISH_MOUTH",
	}
	MSG_BROADCAST_EVENT_value = map[string]int32{
		"MBE_UNKNOWN":    0,
		"MBE_CATCH_FISH": 1,
		"MBE_FISH_MOUTH": 2,
	}
)

func (x MSG_BROADCAST_EVENT) Enum() *MSG_BROADCAST_EVENT {
	p := new(MSG_BROADCAST_EVENT)
	*p = x
	return p
}

func (x MSG_BROADCAST_EVENT) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MSG_BROADCAST_EVENT) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[106].Descriptor()
}

func (MSG_BROADCAST_EVENT) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[106]
}

func (x MSG_BROADCAST_EVENT) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MSG_BROADCAST_EVENT.Descriptor instead.
func (MSG_BROADCAST_EVENT) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{106}
}

// 钓场地图水下结构
type UNDER_WATER_STRUCTURE int32

const (
	UNDER_WATER_STRUCTURE_UWS_OPEN_WATER  UNDER_WATER_STRUCTURE = 0  // 开放水域
	UNDER_WATER_STRUCTURE_UWS_WATER_GRASS UNDER_WATER_STRUCTURE = 1  // 水草
	UNDER_WATER_STRUCTURE_UWS_STONE       UNDER_WATER_STRUCTURE = 2  // 石头
	UNDER_WATER_STRUCTURE_UWS_DRIFTWOOD   UNDER_WATER_STRUCTURE = 3  // 沉木
	UNDER_WATER_STRUCTURE_UWS_PIER        UNDER_WATER_STRUCTURE = 4  // 桥墩
	UNDER_WATER_STRUCTURE_UWS_DEEPPIT     UNDER_WATER_STRUCTURE = 5  // 深坑
	UNDER_WATER_STRUCTURE_UWS_RIDGE       UNDER_WATER_STRUCTURE = 6  // 尖脊
	UNDER_WATER_STRUCTURE_UWS_FAULT       UNDER_WATER_STRUCTURE = 7  //断层
	UNDER_WATER_STRUCTURE_UWS_ROCKSHELF   UNDER_WATER_STRUCTURE = 8  //岩架
	UNDER_WATER_STRUCTURE_UWS_BAY         UNDER_WATER_STRUCTURE = 9  //湾子
	UNDER_WATER_STRUCTURE_UWS_MUD         UNDER_WATER_STRUCTURE = 10 //泥底
	UNDER_WATER_STRUCTURE_UWS_GRAVEL      UNDER_WATER_STRUCTURE = 11 //碎石底
)

// Enum value maps for UNDER_WATER_STRUCTURE.
var (
	UNDER_WATER_STRUCTURE_name = map[int32]string{
		0:  "UWS_OPEN_WATER",
		1:  "UWS_WATER_GRASS",
		2:  "UWS_STONE",
		3:  "UWS_DRIFTWOOD",
		4:  "UWS_PIER",
		5:  "UWS_DEEPPIT",
		6:  "UWS_RIDGE",
		7:  "UWS_FAULT",
		8:  "UWS_ROCKSHELF",
		9:  "UWS_BAY",
		10: "UWS_MUD",
		11: "UWS_GRAVEL",
	}
	UNDER_WATER_STRUCTURE_value = map[string]int32{
		"UWS_OPEN_WATER":  0,
		"UWS_WATER_GRASS": 1,
		"UWS_STONE":       2,
		"UWS_DRIFTWOOD":   3,
		"UWS_PIER":        4,
		"UWS_DEEPPIT":     5,
		"UWS_RIDGE":       6,
		"UWS_FAULT":       7,
		"UWS_ROCKSHELF":   8,
		"UWS_BAY":         9,
		"UWS_MUD":         10,
		"UWS_GRAVEL":      11,
	}
)

func (x UNDER_WATER_STRUCTURE) Enum() *UNDER_WATER_STRUCTURE {
	p := new(UNDER_WATER_STRUCTURE)
	*p = x
	return p
}

func (x UNDER_WATER_STRUCTURE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UNDER_WATER_STRUCTURE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[107].Descriptor()
}

func (UNDER_WATER_STRUCTURE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[107]
}

func (x UNDER_WATER_STRUCTURE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UNDER_WATER_STRUCTURE.Descriptor instead.
func (UNDER_WATER_STRUCTURE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{107}
}

// 地图水层类型
type MAP_WATER_LAYER_TYPE int32

const (
	MAP_WATER_LAYER_TYPE_MWLT_UNKNOWN MAP_WATER_LAYER_TYPE = 0
	MAP_WATER_LAYER_TYPE_MWLT_SURFACE MAP_WATER_LAYER_TYPE = 1 // 表层
	MAP_WATER_LAYER_TYPE_MWLT_MIDDLE  MAP_WATER_LAYER_TYPE = 2 // 中层
	MAP_WATER_LAYER_TYPE_MWLT_BOTTOM  MAP_WATER_LAYER_TYPE = 3 // 底层
)

// Enum value maps for MAP_WATER_LAYER_TYPE.
var (
	MAP_WATER_LAYER_TYPE_name = map[int32]string{
		0: "MWLT_UNKNOWN",
		1: "MWLT_SURFACE",
		2: "MWLT_MIDDLE",
		3: "MWLT_BOTTOM",
	}
	MAP_WATER_LAYER_TYPE_value = map[string]int32{
		"MWLT_UNKNOWN": 0,
		"MWLT_SURFACE": 1,
		"MWLT_MIDDLE":  2,
		"MWLT_BOTTOM":  3,
	}
)

func (x MAP_WATER_LAYER_TYPE) Enum() *MAP_WATER_LAYER_TYPE {
	p := new(MAP_WATER_LAYER_TYPE)
	*p = x
	return p
}

func (x MAP_WATER_LAYER_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MAP_WATER_LAYER_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[108].Descriptor()
}

func (MAP_WATER_LAYER_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[108]
}

func (x MAP_WATER_LAYER_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MAP_WATER_LAYER_TYPE.Descriptor instead.
func (MAP_WATER_LAYER_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{108}
}

type FISHING_TRICK_LEVEL_TYPE int32

const (
	FISHING_TRICK_LEVEL_TYPE_FTLT_ZERO   FISHING_TRICK_LEVEL_TYPE = 0 // 零档
	FISHING_TRICK_LEVEL_TYPE_FTLT_FIRST  FISHING_TRICK_LEVEL_TYPE = 1 // 一档
	FISHING_TRICK_LEVEL_TYPE_FTLT_SECOND FISHING_TRICK_LEVEL_TYPE = 2 // 二档
	FISHING_TRICK_LEVEL_TYPE_FTLT_THIRD  FISHING_TRICK_LEVEL_TYPE = 3 // 三挡
)

// Enum value maps for FISHING_TRICK_LEVEL_TYPE.
var (
	FISHING_TRICK_LEVEL_TYPE_name = map[int32]string{
		0: "FTLT_ZERO",
		1: "FTLT_FIRST",
		2: "FTLT_SECOND",
		3: "FTLT_THIRD",
	}
	FISHING_TRICK_LEVEL_TYPE_value = map[string]int32{
		"FTLT_ZERO":   0,
		"FTLT_FIRST":  1,
		"FTLT_SECOND": 2,
		"FTLT_THIRD":  3,
	}
)

func (x FISHING_TRICK_LEVEL_TYPE) Enum() *FISHING_TRICK_LEVEL_TYPE {
	p := new(FISHING_TRICK_LEVEL_TYPE)
	*p = x
	return p
}

func (x FISHING_TRICK_LEVEL_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_TRICK_LEVEL_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[109].Descriptor()
}

func (FISHING_TRICK_LEVEL_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[109]
}

func (x FISHING_TRICK_LEVEL_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_TRICK_LEVEL_TYPE.Descriptor instead.
func (FISHING_TRICK_LEVEL_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{109}
}

// 钓鱼增益类型
type FISHING_BUFF_TYPE int32

const (
	FISHING_BUFF_TYPE_FBT_NONE          FISHING_BUFF_TYPE = 0   // 无
	FISHING_BUFF_TYPE_FBT_BAIT_TRICK    FISHING_BUFF_TYPE = 101 // 动作(技法)
	FISHING_BUFF_TYPE_FBT_BAIT_SPLASH   FISHING_BUFF_TYPE = 102 // 水花
	FISHING_BUFF_TYPE_FBT_BAIT_NOISE    FISHING_BUFF_TYPE = 103 // 音效
	FISHING_BUFF_TYPE_FBT_BAIT_LIGHTING FISHING_BUFF_TYPE = 104 // 光效
)

// Enum value maps for FISHING_BUFF_TYPE.
var (
	FISHING_BUFF_TYPE_name = map[int32]string{
		0:   "FBT_NONE",
		101: "FBT_BAIT_TRICK",
		102: "FBT_BAIT_SPLASH",
		103: "FBT_BAIT_NOISE",
		104: "FBT_BAIT_LIGHTING",
	}
	FISHING_BUFF_TYPE_value = map[string]int32{
		"FBT_NONE":          0,
		"FBT_BAIT_TRICK":    101,
		"FBT_BAIT_SPLASH":   102,
		"FBT_BAIT_NOISE":    103,
		"FBT_BAIT_LIGHTING": 104,
	}
)

func (x FISHING_BUFF_TYPE) Enum() *FISHING_BUFF_TYPE {
	p := new(FISHING_BUFF_TYPE)
	*p = x
	return p
}

func (x FISHING_BUFF_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_BUFF_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[110].Descriptor()
}

func (FISHING_BUFF_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[110]
}

func (x FISHING_BUFF_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_BUFF_TYPE.Descriptor instead.
func (FISHING_BUFF_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{110}
}

// 饵动作技法类型
type FISHING_BAIT_TRICK_TYPE int32

const (
	FISHING_BAIT_TRICK_TYPE_FBTT_NONE                 FISHING_BAIT_TRICK_TYPE = 0 // 无
	FISHING_BAIT_TRICK_TYPE_FBTT_RULELESS             FISHING_BAIT_TRICK_TYPE = 1 // 无规则运动
	FISHING_BAIT_TRICK_TYPE_FBTT_FORZEN               FISHING_BAIT_TRICK_TYPE = 2 // 静止
	FISHING_BAIT_TRICK_TYPE_FBTT_DRIFTING             FISHING_BAIT_TRICK_TYPE = 3 // 下沉(自由落体)
	FISHING_BAIT_TRICK_TYPE_FBTT_STRAIGHT             FISHING_BAIT_TRICK_TYPE = 4 // 直线运动
	FISHING_BAIT_TRICK_TYPE_FBTT_STRAIGHT_AND_SHAKING FISHING_BAIT_TRICK_TYPE = 5 // 直线+摇摆
	FISHING_BAIT_TRICK_TYPE_FBTT_SHAKING              FISHING_BAIT_TRICK_TYPE = 6 // 摇摆
	FISHING_BAIT_TRICK_TYPE_FBTT_TWITCHING            FISHING_BAIT_TRICK_TYPE = 7 // 抽搐
	FISHING_BAIT_TRICK_TYPE_FBTT_STOP_AND_GO          FISHING_BAIT_TRICK_TYPE = 8 // 走停
)

// Enum value maps for FISHING_BAIT_TRICK_TYPE.
var (
	FISHING_BAIT_TRICK_TYPE_name = map[int32]string{
		0: "FBTT_NONE",
		1: "FBTT_RULELESS",
		2: "FBTT_FORZEN",
		3: "FBTT_DRIFTING",
		4: "FBTT_STRAIGHT",
		5: "FBTT_STRAIGHT_AND_SHAKING",
		6: "FBTT_SHAKING",
		7: "FBTT_TWITCHING",
		8: "FBTT_STOP_AND_GO",
	}
	FISHING_BAIT_TRICK_TYPE_value = map[string]int32{
		"FBTT_NONE":                 0,
		"FBTT_RULELESS":             1,
		"FBTT_FORZEN":               2,
		"FBTT_DRIFTING":             3,
		"FBTT_STRAIGHT":             4,
		"FBTT_STRAIGHT_AND_SHAKING": 5,
		"FBTT_SHAKING":              6,
		"FBTT_TWITCHING":            7,
		"FBTT_STOP_AND_GO":          8,
	}
)

func (x FISHING_BAIT_TRICK_TYPE) Enum() *FISHING_BAIT_TRICK_TYPE {
	p := new(FISHING_BAIT_TRICK_TYPE)
	*p = x
	return p
}

func (x FISHING_BAIT_TRICK_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_BAIT_TRICK_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[111].Descriptor()
}

func (FISHING_BAIT_TRICK_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[111]
}

func (x FISHING_BAIT_TRICK_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_BAIT_TRICK_TYPE.Descriptor instead.
func (FISHING_BAIT_TRICK_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{111}
}

// 饵水花类型
type FISHING_BAIT_SPLASH_TYPE int32

const (
	FISHING_BAIT_SPLASH_TYPE_FBST_NONE   FISHING_BAIT_SPLASH_TYPE = 0 // 无
	FISHING_BAIT_SPLASH_TYPE_FBST_SMALL  FISHING_BAIT_SPLASH_TYPE = 1 // 小
	FISHING_BAIT_SPLASH_TYPE_FBST_MIDDLE FISHING_BAIT_SPLASH_TYPE = 2 // 中
	FISHING_BAIT_SPLASH_TYPE_FBST_BIG    FISHING_BAIT_SPLASH_TYPE = 3 // 大
)

// Enum value maps for FISHING_BAIT_SPLASH_TYPE.
var (
	FISHING_BAIT_SPLASH_TYPE_name = map[int32]string{
		0: "FBST_NONE",
		1: "FBST_SMALL",
		2: "FBST_MIDDLE",
		3: "FBST_BIG",
	}
	FISHING_BAIT_SPLASH_TYPE_value = map[string]int32{
		"FBST_NONE":   0,
		"FBST_SMALL":  1,
		"FBST_MIDDLE": 2,
		"FBST_BIG":    3,
	}
)

func (x FISHING_BAIT_SPLASH_TYPE) Enum() *FISHING_BAIT_SPLASH_TYPE {
	p := new(FISHING_BAIT_SPLASH_TYPE)
	*p = x
	return p
}

func (x FISHING_BAIT_SPLASH_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_BAIT_SPLASH_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[112].Descriptor()
}

func (FISHING_BAIT_SPLASH_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[112]
}

func (x FISHING_BAIT_SPLASH_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_BAIT_SPLASH_TYPE.Descriptor instead.
func (FISHING_BAIT_SPLASH_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{112}
}

// 饵音效类型
type FISHING_BAIT_NOISE_TYPE int32

const (
	FISHING_BAIT_NOISE_TYPE_FBNT_NONE   FISHING_BAIT_NOISE_TYPE = 0 // 无
	FISHING_BAIT_NOISE_TYPE_FBNT_LOW    FISHING_BAIT_NOISE_TYPE = 1 // 小
	FISHING_BAIT_NOISE_TYPE_FBNT_MIDDLE FISHING_BAIT_NOISE_TYPE = 2 // 中
	FISHING_BAIT_NOISE_TYPE_FBNT_HIGH   FISHING_BAIT_NOISE_TYPE = 3 // 大
)

// Enum value maps for FISHING_BAIT_NOISE_TYPE.
var (
	FISHING_BAIT_NOISE_TYPE_name = map[int32]string{
		0: "FBNT_NONE",
		1: "FBNT_LOW",
		2: "FBNT_MIDDLE",
		3: "FBNT_HIGH",
	}
	FISHING_BAIT_NOISE_TYPE_value = map[string]int32{
		"FBNT_NONE":   0,
		"FBNT_LOW":    1,
		"FBNT_MIDDLE": 2,
		"FBNT_HIGH":   3,
	}
)

func (x FISHING_BAIT_NOISE_TYPE) Enum() *FISHING_BAIT_NOISE_TYPE {
	p := new(FISHING_BAIT_NOISE_TYPE)
	*p = x
	return p
}

func (x FISHING_BAIT_NOISE_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_BAIT_NOISE_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[113].Descriptor()
}

func (FISHING_BAIT_NOISE_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[113]
}

func (x FISHING_BAIT_NOISE_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_BAIT_NOISE_TYPE.Descriptor instead.
func (FISHING_BAIT_NOISE_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{113}
}

// 饵光效类型
type FISHING_BAIT_LIGHTING_TYPE int32

const (
	FISHING_BAIT_LIGHTING_TYPE_FBLT_NONE FISHING_BAIT_LIGHTING_TYPE = 0 // 无
	FISHING_BAIT_LIGHTING_TYPE_FBLT_WEEK FISHING_BAIT_LIGHTING_TYPE = 1 // 弱(小)
	FISHING_BAIT_LIGHTING_TYPE_FBLT_SOFT FISHING_BAIT_LIGHTING_TYPE = 2 // 一般(中)
	FISHING_BAIT_LIGHTING_TYPE_FBLT_HARD FISHING_BAIT_LIGHTING_TYPE = 3 // 强(大)
)

// Enum value maps for FISHING_BAIT_LIGHTING_TYPE.
var (
	FISHING_BAIT_LIGHTING_TYPE_name = map[int32]string{
		0: "FBLT_NONE",
		1: "FBLT_WEEK",
		2: "FBLT_SOFT",
		3: "FBLT_HARD",
	}
	FISHING_BAIT_LIGHTING_TYPE_value = map[string]int32{
		"FBLT_NONE": 0,
		"FBLT_WEEK": 1,
		"FBLT_SOFT": 2,
		"FBLT_HARD": 3,
	}
)

func (x FISHING_BAIT_LIGHTING_TYPE) Enum() *FISHING_BAIT_LIGHTING_TYPE {
	p := new(FISHING_BAIT_LIGHTING_TYPE)
	*p = x
	return p
}

func (x FISHING_BAIT_LIGHTING_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_BAIT_LIGHTING_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[114].Descriptor()
}

func (FISHING_BAIT_LIGHTING_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[114]
}

func (x FISHING_BAIT_LIGHTING_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_BAIT_LIGHTING_TYPE.Descriptor instead.
func (FISHING_BAIT_LIGHTING_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{114}
}

// 鱼破损状态
type FISH_DAMAGED_LV int32

const (
	FISH_DAMAGED_LV_FDL_INTACT FISH_DAMAGED_LV = 0 // 完好
	FISH_DAMAGED_LV_FDL_LV1    FISH_DAMAGED_LV = 1 // 1级破损
	FISH_DAMAGED_LV_FDL_LV2    FISH_DAMAGED_LV = 2 // 2级破损
)

// Enum value maps for FISH_DAMAGED_LV.
var (
	FISH_DAMAGED_LV_name = map[int32]string{
		0: "FDL_INTACT",
		1: "FDL_LV1",
		2: "FDL_LV2",
	}
	FISH_DAMAGED_LV_value = map[string]int32{
		"FDL_INTACT": 0,
		"FDL_LV1":    1,
		"FDL_LV2":    2,
	}
)

func (x FISH_DAMAGED_LV) Enum() *FISH_DAMAGED_LV {
	p := new(FISH_DAMAGED_LV)
	*p = x
	return p
}

func (x FISH_DAMAGED_LV) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISH_DAMAGED_LV) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[115].Descriptor()
}

func (FISH_DAMAGED_LV) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[115]
}

func (x FISH_DAMAGED_LV) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISH_DAMAGED_LV.Descriptor instead.
func (FISH_DAMAGED_LV) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{115}
}

// 搏鱼对抗类型
type BATTLING_FIGHT_TYPE int32

const (
	BATTLING_FIGHT_TYPE_BFT_UNAVAILING BATTLING_FIGHT_TYPE = 0 // 无效对抗
	BATTLING_FIGHT_TYPE_BFT_BEST       BATTLING_FIGHT_TYPE = 1 // 最优对抗
	BATTLING_FIGHT_TYPE_BFT_GOOD       BATTLING_FIGHT_TYPE = 2 // 次优对抗
	BATTLING_FIGHT_TYPE_BFT_DRASTIC    BATTLING_FIGHT_TYPE = 3 // 高压对抗
)

// Enum value maps for BATTLING_FIGHT_TYPE.
var (
	BATTLING_FIGHT_TYPE_name = map[int32]string{
		0: "BFT_UNAVAILING",
		1: "BFT_BEST",
		2: "BFT_GOOD",
		3: "BFT_DRASTIC",
	}
	BATTLING_FIGHT_TYPE_value = map[string]int32{
		"BFT_UNAVAILING": 0,
		"BFT_BEST":       1,
		"BFT_GOOD":       2,
		"BFT_DRASTIC":    3,
	}
)

func (x BATTLING_FIGHT_TYPE) Enum() *BATTLING_FIGHT_TYPE {
	p := new(BATTLING_FIGHT_TYPE)
	*p = x
	return p
}

func (x BATTLING_FIGHT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BATTLING_FIGHT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[116].Descriptor()
}

func (BATTLING_FIGHT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[116]
}

func (x BATTLING_FIGHT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BATTLING_FIGHT_TYPE.Descriptor instead.
func (BATTLING_FIGHT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{116}
}

// 钓组钓法类型
type FISHING_RIG_TYPE int32

const (
	FISHING_RIG_TYPE_FRT_UNKNOWN FISHING_RIG_TYPE = 0 // 无
	FISHING_RIG_TYPE_FRT_LURE    FISHING_RIG_TYPE = 1 // 路亚
	FISHING_RIG_TYPE_FRT_FLOAT   FISHING_RIG_TYPE = 2 // 浮钓
	FISHING_RIG_TYPE_FRT_ROCK    FISHING_RIG_TYPE = 3 // 矶钓
)

// Enum value maps for FISHING_RIG_TYPE.
var (
	FISHING_RIG_TYPE_name = map[int32]string{
		0: "FRT_UNKNOWN",
		1: "FRT_LURE",
		2: "FRT_FLOAT",
		3: "FRT_ROCK",
	}
	FISHING_RIG_TYPE_value = map[string]int32{
		"FRT_UNKNOWN": 0,
		"FRT_LURE":    1,
		"FRT_FLOAT":   2,
		"FRT_ROCK":    3,
	}
)

func (x FISHING_RIG_TYPE) Enum() *FISHING_RIG_TYPE {
	p := new(FISHING_RIG_TYPE)
	*p = x
	return p
}

func (x FISHING_RIG_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FISHING_RIG_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[117].Descriptor()
}

func (FISHING_RIG_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[117]
}

func (x FISHING_RIG_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FISHING_RIG_TYPE.Descriptor instead.
func (FISHING_RIG_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{117}
}

// 排名通用数据封装
type RANK_INFO_KEY int32

const (
	RANK_INFO_KEY_RIK_UNKNOWN     RANK_INFO_KEY = 0
	RANK_INFO_KEY_RIK_FISH_ID     RANK_INFO_KEY = 1 // 鱼id
	RANK_INFO_KEY_RIK_FISH_VALUE  RANK_INFO_KEY = 2 // 鱼价值
	RANK_INFO_KEY_RIK_BAIT_ID     RANK_INFO_KEY = 3 // 饵id
	RANK_INFO_KEY_RIK_FISH_WEIGHT RANK_INFO_KEY = 4 // 鱼重量（克）
	RANK_INFO_KEY_RIK_FISH_POND   RANK_INFO_KEY = 5 // 钓场id
	RANK_INFO_KEY_RIK_FISH_LENGTH RANK_INFO_KEY = 6 // 鱼长度（厘米）
	RANK_INFO_KEY_RIK_FISH_BROKEN RANK_INFO_KEY = 7 // 鱼破损
)

// Enum value maps for RANK_INFO_KEY.
var (
	RANK_INFO_KEY_name = map[int32]string{
		0: "RIK_UNKNOWN",
		1: "RIK_FISH_ID",
		2: "RIK_FISH_VALUE",
		3: "RIK_BAIT_ID",
		4: "RIK_FISH_WEIGHT",
		5: "RIK_FISH_POND",
		6: "RIK_FISH_LENGTH",
		7: "RIK_FISH_BROKEN",
	}
	RANK_INFO_KEY_value = map[string]int32{
		"RIK_UNKNOWN":     0,
		"RIK_FISH_ID":     1,
		"RIK_FISH_VALUE":  2,
		"RIK_BAIT_ID":     3,
		"RIK_FISH_WEIGHT": 4,
		"RIK_FISH_POND":   5,
		"RIK_FISH_LENGTH": 6,
		"RIK_FISH_BROKEN": 7,
	}
)

func (x RANK_INFO_KEY) Enum() *RANK_INFO_KEY {
	p := new(RANK_INFO_KEY)
	*p = x
	return p
}

func (x RANK_INFO_KEY) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RANK_INFO_KEY) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[118].Descriptor()
}

func (RANK_INFO_KEY) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[118]
}

func (x RANK_INFO_KEY) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RANK_INFO_KEY.Descriptor instead.
func (RANK_INFO_KEY) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{118}
}

// 刷新时间类型
type FLUSH_TIME_TYPE int32

const (
	FLUSH_TIME_TYPE_FTT_PERMANENT FLUSH_TIME_TYPE = 0 // 永久
	FLUSH_TIME_TYPE_FTT_MONTH     FLUSH_TIME_TYPE = 1 // 每月
	FLUSH_TIME_TYPE_FTT_WEEK      FLUSH_TIME_TYPE = 2 // 每周
	FLUSH_TIME_TYPE_FTT_DAY       FLUSH_TIME_TYPE = 3 // 每天
)

// Enum value maps for FLUSH_TIME_TYPE.
var (
	FLUSH_TIME_TYPE_name = map[int32]string{
		0: "FTT_PERMANENT",
		1: "FTT_MONTH",
		2: "FTT_WEEK",
		3: "FTT_DAY",
	}
	FLUSH_TIME_TYPE_value = map[string]int32{
		"FTT_PERMANENT": 0,
		"FTT_MONTH":     1,
		"FTT_WEEK":      2,
		"FTT_DAY":       3,
	}
)

func (x FLUSH_TIME_TYPE) Enum() *FLUSH_TIME_TYPE {
	p := new(FLUSH_TIME_TYPE)
	*p = x
	return p
}

func (x FLUSH_TIME_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FLUSH_TIME_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[119].Descriptor()
}

func (FLUSH_TIME_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[119]
}

func (x FLUSH_TIME_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FLUSH_TIME_TYPE.Descriptor instead.
func (FLUSH_TIME_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{119}
}

// 排行榜类型
type RANK_TYPE int32

const (
	RANK_TYPE_RANK_TYPE_UNKNOWN RANK_TYPE = 0
	RANK_TYPE_RT_FISHING_WEIGHT RANK_TYPE = 1 // 钓鱼重量榜单
	RANK_TYPE_RT_FISHING_VALUE  RANK_TYPE = 2 // 钓鱼价值榜单
)

// Enum value maps for RANK_TYPE.
var (
	RANK_TYPE_name = map[int32]string{
		0: "RANK_TYPE_UNKNOWN",
		1: "RT_FISHING_WEIGHT",
		2: "RT_FISHING_VALUE",
	}
	RANK_TYPE_value = map[string]int32{
		"RANK_TYPE_UNKNOWN": 0,
		"RT_FISHING_WEIGHT": 1,
		"RT_FISHING_VALUE":  2,
	}
)

func (x RANK_TYPE) Enum() *RANK_TYPE {
	p := new(RANK_TYPE)
	*p = x
	return p
}

func (x RANK_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RANK_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[120].Descriptor()
}

func (RANK_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[120]
}

func (x RANK_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RANK_TYPE.Descriptor instead.
func (RANK_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{120}
}

// 公告标签类型定义
type ANN_TAG_TYPE int32

const (
	ANN_TAG_TYPE_TAG_NONE ANN_TAG_TYPE = 0 // 无
	ANN_TAG_TYPE_TAG_NEW  ANN_TAG_TYPE = 1 // NEW
	ANN_TAG_TYPE_TAG_HOT  ANN_TAG_TYPE = 2 // HOT
)

// Enum value maps for ANN_TAG_TYPE.
var (
	ANN_TAG_TYPE_name = map[int32]string{
		0: "TAG_NONE",
		1: "TAG_NEW",
		2: "TAG_HOT",
	}
	ANN_TAG_TYPE_value = map[string]int32{
		"TAG_NONE": 0,
		"TAG_NEW":  1,
		"TAG_HOT":  2,
	}
)

func (x ANN_TAG_TYPE) Enum() *ANN_TAG_TYPE {
	p := new(ANN_TAG_TYPE)
	*p = x
	return p
}

func (x ANN_TAG_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ANN_TAG_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[121].Descriptor()
}

func (ANN_TAG_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[121]
}

func (x ANN_TAG_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ANN_TAG_TYPE.Descriptor instead.
func (ANN_TAG_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{121}
}

// 公告生效类型定义
type ANN_EFFECT_TYPE int32

const (
	ANN_EFFECT_TYPE_EFFECT_NONE     ANN_EFFECT_TYPE = 0 // 无
	ANN_EFFECT_TYPE_EFFECT_LOGIN    ANN_EFFECT_TYPE = 1 // 登录页
	ANN_EFFECT_TYPE_EFFECT_HOMEPAGE ANN_EFFECT_TYPE = 2 // 游戏首页
)

// Enum value maps for ANN_EFFECT_TYPE.
var (
	ANN_EFFECT_TYPE_name = map[int32]string{
		0: "EFFECT_NONE",
		1: "EFFECT_LOGIN",
		2: "EFFECT_HOMEPAGE",
	}
	ANN_EFFECT_TYPE_value = map[string]int32{
		"EFFECT_NONE":     0,
		"EFFECT_LOGIN":    1,
		"EFFECT_HOMEPAGE": 2,
	}
)

func (x ANN_EFFECT_TYPE) Enum() *ANN_EFFECT_TYPE {
	p := new(ANN_EFFECT_TYPE)
	*p = x
	return p
}

func (x ANN_EFFECT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ANN_EFFECT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[122].Descriptor()
}

func (ANN_EFFECT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[122]
}

func (x ANN_EFFECT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ANN_EFFECT_TYPE.Descriptor instead.
func (ANN_EFFECT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{122}
}

// 公告显示类型定义
type ANN_DISPLAY_TYPE int32

const (
	ANN_DISPLAY_TYPE_DISPLAY_NONE    ANN_DISPLAY_TYPE = 0 // 无
	ANN_DISPLAY_TYPE_DISPLAY_DAILY   ANN_DISPLAY_TYPE = 1 // 每日一次
	ANN_DISPLAY_TYPE_DISPLAY_HISTORY ANN_DISPLAY_TYPE = 2 // 历史一次
	ANN_DISPLAY_TYPE_DISPLAY_LOGIN   ANN_DISPLAY_TYPE = 3 // 每次登录
)

// Enum value maps for ANN_DISPLAY_TYPE.
var (
	ANN_DISPLAY_TYPE_name = map[int32]string{
		0: "DISPLAY_NONE",
		1: "DISPLAY_DAILY",
		2: "DISPLAY_HISTORY",
		3: "DISPLAY_LOGIN",
	}
	ANN_DISPLAY_TYPE_value = map[string]int32{
		"DISPLAY_NONE":    0,
		"DISPLAY_DAILY":   1,
		"DISPLAY_HISTORY": 2,
		"DISPLAY_LOGIN":   3,
	}
)

func (x ANN_DISPLAY_TYPE) Enum() *ANN_DISPLAY_TYPE {
	p := new(ANN_DISPLAY_TYPE)
	*p = x
	return p
}

func (x ANN_DISPLAY_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ANN_DISPLAY_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[123].Descriptor()
}

func (ANN_DISPLAY_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[123]
}

func (x ANN_DISPLAY_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ANN_DISPLAY_TYPE.Descriptor instead.
func (ANN_DISPLAY_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{123}
}

// 公告跳转类型定义
type ANN_JUMP_TYPE int32

const (
	ANN_JUMP_TYPE_JUMP_NONE         ANN_JUMP_TYPE = 0 // 无
	ANN_JUMP_TYPE_JUMP_STORE        ANN_JUMP_TYPE = 1 // 商城
	ANN_JUMP_TYPE_JUMP_ACTIVE       ANN_JUMP_TYPE = 2 // 活动中心
	ANN_JUMP_TYPE_JUMP_ANNOUNCEMENT ANN_JUMP_TYPE = 3 // 公告中心
	ANN_JUMP_TYPE_JUMP_SELECT_POND  ANN_JUMP_TYPE = 4 // 选场中心
)

// Enum value maps for ANN_JUMP_TYPE.
var (
	ANN_JUMP_TYPE_name = map[int32]string{
		0: "JUMP_NONE",
		1: "JUMP_STORE",
		2: "JUMP_ACTIVE",
		3: "JUMP_ANNOUNCEMENT",
		4: "JUMP_SELECT_POND",
	}
	ANN_JUMP_TYPE_value = map[string]int32{
		"JUMP_NONE":         0,
		"JUMP_STORE":        1,
		"JUMP_ACTIVE":       2,
		"JUMP_ANNOUNCEMENT": 3,
		"JUMP_SELECT_POND":  4,
	}
)

func (x ANN_JUMP_TYPE) Enum() *ANN_JUMP_TYPE {
	p := new(ANN_JUMP_TYPE)
	*p = x
	return p
}

func (x ANN_JUMP_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ANN_JUMP_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[124].Descriptor()
}

func (ANN_JUMP_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[124]
}

func (x ANN_JUMP_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ANN_JUMP_TYPE.Descriptor instead.
func (ANN_JUMP_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{124}
}

type DATA_REPORT_TYPE int32

const (
	DATA_REPORT_TYPE_DRT_UNKNOWN      DATA_REPORT_TYPE = 0 // 未知类型
	DATA_REPORT_TYPE_DRT_APP_OPEN     DATA_REPORT_TYPE = 1 // 打开上报
	DATA_REPORT_TYPE_DRT_NOVICE_SCENE DATA_REPORT_TYPE = 2 // 新手引导
	DATA_REPORT_TYPE_DRT_FISHING      DATA_REPORT_TYPE = 3 // 钓鱼记录
)

// Enum value maps for DATA_REPORT_TYPE.
var (
	DATA_REPORT_TYPE_name = map[int32]string{
		0: "DRT_UNKNOWN",
		1: "DRT_APP_OPEN",
		2: "DRT_NOVICE_SCENE",
		3: "DRT_FISHING",
	}
	DATA_REPORT_TYPE_value = map[string]int32{
		"DRT_UNKNOWN":      0,
		"DRT_APP_OPEN":     1,
		"DRT_NOVICE_SCENE": 2,
		"DRT_FISHING":      3,
	}
)

func (x DATA_REPORT_TYPE) Enum() *DATA_REPORT_TYPE {
	p := new(DATA_REPORT_TYPE)
	*p = x
	return p
}

func (x DATA_REPORT_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DATA_REPORT_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[125].Descriptor()
}

func (DATA_REPORT_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[125]
}

func (x DATA_REPORT_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DATA_REPORT_TYPE.Descriptor instead.
func (DATA_REPORT_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{125}
}

// CDK 批次状态
type CDK_BATCH_STATUS int32

const (
	CDK_BATCH_STATUS_CBS_UNKNOWN CDK_BATCH_STATUS = 0
	CDK_BATCH_STATUS_CBS_ENABLE  CDK_BATCH_STATUS = 1 // 启用
	CDK_BATCH_STATUS_CBS_DISABLE CDK_BATCH_STATUS = 2 // 作废
)

// Enum value maps for CDK_BATCH_STATUS.
var (
	CDK_BATCH_STATUS_name = map[int32]string{
		0: "CBS_UNKNOWN",
		1: "CBS_ENABLE",
		2: "CBS_DISABLE",
	}
	CDK_BATCH_STATUS_value = map[string]int32{
		"CBS_UNKNOWN": 0,
		"CBS_ENABLE":  1,
		"CBS_DISABLE": 2,
	}
)

func (x CDK_BATCH_STATUS) Enum() *CDK_BATCH_STATUS {
	p := new(CDK_BATCH_STATUS)
	*p = x
	return p
}

func (x CDK_BATCH_STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CDK_BATCH_STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[126].Descriptor()
}

func (CDK_BATCH_STATUS) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[126]
}

func (x CDK_BATCH_STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CDK_BATCH_STATUS.Descriptor instead.
func (CDK_BATCH_STATUS) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{126}
}

// CDK生成方式枚举
type CDK_GENERATION_OPTION int32

const (
	CDK_GENERATION_OPTION_CGO_UNKNOWN CDK_GENERATION_OPTION = 0
	CDK_GENERATION_OPTION_CGO_RANDOM  CDK_GENERATION_OPTION = 1 // 随机生成
	CDK_GENERATION_OPTION_CGO_CUSTOM  CDK_GENERATION_OPTION = 2 // 自定义
)

// Enum value maps for CDK_GENERATION_OPTION.
var (
	CDK_GENERATION_OPTION_name = map[int32]string{
		0: "CGO_UNKNOWN",
		1: "CGO_RANDOM",
		2: "CGO_CUSTOM",
	}
	CDK_GENERATION_OPTION_value = map[string]int32{
		"CGO_UNKNOWN": 0,
		"CGO_RANDOM":  1,
		"CGO_CUSTOM":  2,
	}
)

func (x CDK_GENERATION_OPTION) Enum() *CDK_GENERATION_OPTION {
	p := new(CDK_GENERATION_OPTION)
	*p = x
	return p
}

func (x CDK_GENERATION_OPTION) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CDK_GENERATION_OPTION) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[127].Descriptor()
}

func (CDK_GENERATION_OPTION) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[127]
}

func (x CDK_GENERATION_OPTION) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CDK_GENERATION_OPTION.Descriptor instead.
func (CDK_GENERATION_OPTION) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{127}
}

// 活动类型
type ACTIVITY_TYPE int32

const (
	ACTIVITY_TYPE_AT_UNKNOWN        ACTIVITY_TYPE = 0 // 未知
	ACTIVITY_TYPE_AT_WEIGHT_PURSUIT ACTIVITY_TYPE = 1 // 爆护之路（重量追求）
)

// Enum value maps for ACTIVITY_TYPE.
var (
	ACTIVITY_TYPE_name = map[int32]string{
		0: "AT_UNKNOWN",
		1: "AT_WEIGHT_PURSUIT",
	}
	ACTIVITY_TYPE_value = map[string]int32{
		"AT_UNKNOWN":        0,
		"AT_WEIGHT_PURSUIT": 1,
	}
)

func (x ACTIVITY_TYPE) Enum() *ACTIVITY_TYPE {
	p := new(ACTIVITY_TYPE)
	*p = x
	return p
}

func (x ACTIVITY_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ACTIVITY_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[128].Descriptor()
}

func (ACTIVITY_TYPE) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[128]
}

func (x ACTIVITY_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ACTIVITY_TYPE.Descriptor instead.
func (ACTIVITY_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{128}
}

var File_enum_proto protoreflect.FileDescriptor

var file_enum_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2a, 0x8b, 0x01, 0x0a, 0x0d, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x54, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x59, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x54, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x50, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x0e, 0x0a,
	0x0a, 0x50, 0x54, 0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x04, 0x12, 0x0b, 0x0a,
	0x07, 0x50, 0x54, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x54,
	0x5f, 0x48, 0x35, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x52,
	0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x4f, 0x4c, 0x45,
	0x10, 0x08, 0x2a, 0x2a, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44,
	0x12, 0x0c, 0x0a, 0x08, 0x50, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x50, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x45, 0x52, 0x10, 0x01, 0x2a, 0x5b,
	0x0a, 0x0c, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x09, 0x43,
	0x54, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0xe9, 0x07, 0x12, 0x0e, 0x0a, 0x09, 0x43,
	0x54, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10, 0xea, 0x07, 0x12, 0x10, 0x0a, 0x0b, 0x43,
	0x54, 0x5f, 0x41, 0x50, 0x50, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0xeb, 0x07, 0x12, 0x0c, 0x0a,
	0x07, 0x43, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0xd1, 0x0f, 0x2a, 0x68, 0x0a, 0x08, 0x45,
	0x4e, 0x56, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x54, 0x5f, 0x49, 0x4e,
	0x49, 0x54, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x45, 0x54, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0a, 0x0a,
	0x06, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x54, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x5f, 0x41, 0x55, 0x44,
	0x49, 0x54, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4d,
	0x49, 0x5a, 0x45, 0x10, 0x06, 0x2a, 0x56, 0x0a, 0x0c, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x4e, 0x54, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x04, 0x2a, 0x54, 0x0a,
	0x0d, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b,
	0x0a, 0x07, 0x4c, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4c,
	0x54, 0x5f, 0x45, 0x4e, 0x5f, 0x55, 0x53, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x54, 0x5f,
	0x5a, 0x48, 0x5f, 0x43, 0x4e, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x54, 0x5f, 0x5a, 0x48,
	0x5f, 0x54, 0x57, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x54, 0x5f, 0x44, 0x45, 0x5f, 0x44,
	0x45, 0x10, 0x04, 0x2a, 0x36, 0x0a, 0x0b, 0x47, 0x52, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x53, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x47, 0x53, 0x5f, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x47, 0x53, 0x5f, 0x47, 0x72, 0x61, 0x79, 0x10, 0x02, 0x2a, 0x70, 0x0a, 0x0d, 0x47,
	0x52, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x12, 0x0b, 0x0a, 0x07,
	0x47, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x53, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x53,
	0x5f, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0a,
	0x0a, 0x06, 0x47, 0x53, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x47, 0x53,
	0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x53,
	0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x05, 0x2a, 0x38, 0x0a,
	0x0d, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b,
	0x0a, 0x07, 0x4c, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c,
	0x54, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x54,
	0x5f, 0x47, 0x52, 0x41, 0x59, 0x10, 0x02, 0x2a, 0x41, 0x0a, 0x11, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0c, 0x0a, 0x08,
	0x46, 0x48, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x48,
	0x54, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x48, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x02, 0x2a, 0x40, 0x0a, 0x0f, 0x57, 0x48,
	0x49, 0x54, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0c, 0x0a,
	0x08, 0x57, 0x46, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x57,
	0x46, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x46,
	0x54, 0x5f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x79, 0x0a, 0x0a,
	0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x54,
	0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x54,
	0x5f, 0x56, 0x49, 0x53, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x54,
	0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x54, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x54, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x54, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x54, 0x5f, 0x47,
	0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10, 0x06, 0x2a, 0x48, 0x0a, 0x0a, 0x41, 0x43, 0x43, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x53, 0x5f, 0x4e, 0x4f, 0x52, 0x4d,
	0x41, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x53, 0x5f, 0x42, 0x41, 0x4e, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x41, 0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x46, 0x46, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x41, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x44, 0x45, 0x4e, 0x10,
	0x03, 0x2a, 0x94, 0x01, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0b,
	0x0a, 0x07, 0x41, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41,
	0x54, 0x5f, 0x56, 0x49, 0x53, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x41, 0x54, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x54,
	0x5f, 0x52, 0x4f, 0x42, 0x4f, 0x54, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x54, 0x5f, 0x46,
	0x41, 0x43, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x0b, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x54, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x45, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x54, 0x5f, 0x47, 0x4f,
	0x4f, 0x47, 0x4c, 0x45, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x54, 0x5f, 0x54, 0x48, 0x49,
	0x52, 0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x64, 0x2a, 0x53, 0x0a, 0x13, 0x42, 0x41, 0x4e, 0x5f,
	0x41, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12,
	0x11, 0x0a, 0x0d, 0x42, 0x41, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x41, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x42, 0x41, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x4d, 0x10, 0x02, 0x2a, 0x2f, 0x0a,
	0x0b, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x12, 0x0f, 0x0a, 0x0b,
	0x4c, 0x49, 0x4d, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x0f, 0x0a,
	0x0b, 0x4c, 0x49, 0x4d, 0x5f, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x01, 0x2a, 0x33,
	0x0a, 0x0c, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x12, 0x0f,
	0x0a, 0x0b, 0x4c, 0x4f, 0x4d, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x4c, 0x4f, 0x4d, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x10, 0x01, 0x2a, 0x37, 0x0a, 0x12, 0x4b, 0x49, 0x43, 0x4b, 0x5f, 0x50, 0x4c, 0x41, 0x59,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x12, 0x0f, 0x0a, 0x0b, 0x4b, 0x50, 0x52,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4b, 0x50,
	0x52, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x2a, 0x43, 0x0a, 0x17,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x4e, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x4e, 0x45, 0x10,
	0x01, 0x2a, 0x45, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x49, 0x46, 0x5f, 0x55, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x09, 0x55, 0x49, 0x46, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45,
	0x10, 0x90, 0xe0, 0xf4, 0x01, 0x12, 0x11, 0x0a, 0x0a, 0x55, 0x49, 0x46, 0x5f, 0x41, 0x56, 0x41,
	0x54, 0x41, 0x52, 0x10, 0xa0, 0xae, 0xf5, 0x01, 0x2a, 0x56, 0x0a, 0x08, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x41, 0x47, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x41, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x41, 0x5f, 0x43, 0x48, 0x49, 0x4c, 0x44,
	0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x41, 0x5f, 0x4a, 0x55, 0x56, 0x45, 0x4e, 0x49, 0x4c,
	0x45, 0x10, 0x10, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x41, 0x5f, 0x59, 0x4f, 0x55, 0x4e, 0x47, 0x10,
	0x12, 0x12, 0x0d, 0x0a, 0x08, 0x55, 0x41, 0x5f, 0x41, 0x44, 0x55, 0x4c, 0x54, 0x10, 0xc8, 0x01,
	0x2a, 0x88, 0x02, 0x0a, 0x10, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4d, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4d, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4d, 0x54,
	0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4d, 0x54, 0x5f, 0x57,
	0x41, 0x52, 0x45, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4d,
	0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x55, 0x4d, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x4d,
	0x54, 0x5f, 0x4e, 0x4f, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4d,
	0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4d, 0x54, 0x5f,
	0x41, 0x43, 0x48, 0x49, 0x45, 0x56, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x10, 0x0a,
	0x0c, 0x55, 0x4d, 0x54, 0x5f, 0x46, 0x42, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x09, 0x12,
	0x10, 0x0a, 0x0c, 0x55, 0x4d, 0x54, 0x5f, 0x47, 0x50, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10,
	0x0a, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4d, 0x54, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x0b, 0x12,
	0x16, 0x0a, 0x12, 0x55, 0x4d, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4c, 0x4c, 0x45, 0x4e, 0x47, 0x45,
	0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4d, 0x54, 0x5f, 0x48,
	0x41, 0x4c, 0x55, 0x45, 0x52, 0x53, 0x57, 0x41, 0x59, 0x10, 0x0d, 0x2a, 0x4c, 0x0a, 0x15, 0x52,
	0x45, 0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x44, 0x4d, 0x53, 0x54, 0x5f, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x44, 0x4d, 0x53, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x44, 0x4d,
	0x53, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0x2f, 0x0a, 0x1a, 0x52, 0x45, 0x44,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x57, 0x41, 0x52, 0x45, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x44, 0x57, 0x53, 0x54,
	0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x2a, 0x31, 0x0a, 0x1c, 0x52, 0x45,
	0x44, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x48, 0x49, 0x45, 0x56, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x44,
	0x41, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x2a, 0x3e, 0x0a,
	0x09, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x47, 0x54,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x54,
	0x5f, 0x52, 0x45, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x47, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x4c, 0x0a,
	0x09, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x54,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x53, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x54, 0x5f,
	0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x54, 0x5f,
	0x45, 0x58, 0x43, 0x4c, 0x55, 0x53, 0x49, 0x56, 0x45, 0x10, 0x03, 0x2a, 0x35, 0x0a, 0x0a, 0x52,
	0x4f, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x5f,
	0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x41, 0x5f, 0x54, 0x41, 0x4b, 0x45,
	0x10, 0x02, 0x2a, 0x36, 0x0a, 0x0a, 0x52, 0x4f, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x52, 0x53, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x52, 0x53, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x2a, 0x6e, 0x0a, 0x0b, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x52, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x52, 0x5f,
	0x54, 0x41, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x52, 0x5f, 0x4e, 0x4f,
	0x54, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x52, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x52, 0x5f, 0x48,
	0x4f, 0x4f, 0x4b, 0x45, 0x44, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x52, 0x5f, 0x46, 0x4f,
	0x52, 0x43, 0x45, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x10, 0x05, 0x2a, 0x5b, 0x0a, 0x0b, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x53, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x53, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x53, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x55, 0x50, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x53, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x53, 0x5f,
	0x54, 0x48, 0x52, 0x4f, 0x57, 0x10, 0x04, 0x2a, 0xab, 0x01, 0x0a, 0x0c, 0x57, 0x45, 0x41, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x54, 0x5f, 0x53,
	0x55, 0x4e, 0x4e, 0x59, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x57, 0x54, 0x5f, 0x43, 0x4c, 0x4f,
	0x55, 0x44, 0x59, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x54, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x43, 0x41, 0x53, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x54, 0x5f, 0x46, 0x49, 0x4e,
	0x45, 0x5f, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x54, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x11,
	0x0a, 0x0d, 0x57, 0x54, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x52, 0x41, 0x49, 0x4e, 0x10,
	0x06, 0x12, 0x0d, 0x0a, 0x09, 0x57, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x4d, 0x59, 0x10, 0x07,
	0x12, 0x13, 0x0a, 0x0f, 0x57, 0x54, 0x5f, 0x54, 0x48, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x53, 0x54,
	0x4f, 0x52, 0x4d, 0x10, 0x08, 0x2a, 0x38, 0x0a, 0x0f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x41,
	0x52, 0x45, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x41, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x41, 0x54,
	0x5f, 0x53, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x2a,
	0x76, 0x0a, 0x0d, 0x4f, 0x42, 0x53, 0x54, 0x41, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x4f, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x54,
	0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x4f, 0x54, 0x5f, 0x50, 0x45, 0x42, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b,
	0x4f, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a,
	0x07, 0x4f, 0x54, 0x5f, 0x57, 0x4f, 0x4f, 0x44, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x54,
	0x5f, 0x52, 0x4f, 0x43, 0x4b, 0x10, 0x06, 0x2a, 0x99, 0x01, 0x0a, 0x0e, 0x44, 0x49, 0x52, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x5f, 0x4e,
	0x4f, 0x52, 0x54, 0x48, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x5f, 0x4e, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x45, 0x41, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x5f, 0x45, 0x41,
	0x53, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x5f, 0x53, 0x4f, 0x55, 0x54, 0x48, 0x5f,
	0x45, 0x41, 0x53, 0x54, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x5f, 0x53, 0x4f, 0x55, 0x54,
	0x48, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x5f, 0x53, 0x4f, 0x55, 0x54, 0x48, 0x5f, 0x57,
	0x45, 0x53, 0x54, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x5f, 0x57, 0x45, 0x53, 0x54, 0x10,
	0x07, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x5f, 0x4e, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x57, 0x45, 0x53,
	0x54, 0x10, 0x08, 0x2a, 0x48, 0x0a, 0x13, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x45, 0x4e, 0x54, 0x52,
	0x59, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x45,
	0x4f, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x46, 0x45, 0x4f, 0x54, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x46,
	0x45, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x10, 0x02, 0x2a, 0x4c, 0x0a,
	0x15, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4b, 0x4f, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4b, 0x4f, 0x54,
	0x5f, 0x44, 0x49, 0x53, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4b,
	0x4f, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x02, 0x2a, 0xc7, 0x02, 0x0a, 0x11,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x45, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x53, 0x53, 0x10, 0x01,
	0x12, 0x12, 0x0a, 0x0e, 0x46, 0x53, 0x54, 0x5f, 0x43, 0x59, 0x50, 0x52, 0x49, 0x4e, 0x49, 0x44,
	0x41, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x53, 0x54, 0x5f, 0x43, 0x45, 0x4e, 0x54,
	0x52, 0x41, 0x52, 0x43, 0x48, 0x49, 0x44, 0x41, 0x45, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x46,
	0x53, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x4f, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x44, 0x41, 0x45, 0x10,
	0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x53, 0x54, 0x5f, 0x41, 0x4d, 0x49, 0x49, 0x44, 0x41, 0x45,
	0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x53, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x49, 0x44,
	0x41, 0x45, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x53, 0x54, 0x5f, 0x49, 0x43, 0x54, 0x41,
	0x4c, 0x55, 0x52, 0x49, 0x44, 0x41, 0x45, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x53, 0x54,
	0x5f, 0x45, 0x53, 0x4f, 0x43, 0x49, 0x44, 0x41, 0x45, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x46,
	0x53, 0x54, 0x5f, 0x43, 0x4c, 0x55, 0x50, 0x45, 0x49, 0x44, 0x41, 0x45, 0x10, 0x09, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x53, 0x54, 0x5f, 0x4d, 0x4f, 0x52, 0x4f, 0x4e, 0x49, 0x44, 0x41, 0x45, 0x10,
	0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x53, 0x54, 0x5f, 0x4c, 0x45, 0x55, 0x43, 0x49, 0x53, 0x43,
	0x49, 0x44, 0x41, 0x45, 0x10, 0x0b, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x53, 0x54, 0x5f, 0x53, 0x41,
	0x4c, 0x4d, 0x4f, 0x4e, 0x49, 0x44, 0x41, 0x45, 0x10, 0x0c, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x53,
	0x54, 0x5f, 0x54, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x41, 0x45, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e,
	0x46, 0x53, 0x54, 0x5f, 0x53, 0x43, 0x49, 0x41, 0x45, 0x4e, 0x49, 0x44, 0x41, 0x45, 0x10, 0x0e,
	0x12, 0x13, 0x0a, 0x0f, 0x46, 0x53, 0x54, 0x5f, 0x41, 0x4e, 0x47, 0x55, 0x49, 0x4c, 0x4c, 0x49,
	0x44, 0x41, 0x45, 0x10, 0x0f, 0x2a, 0x69, 0x0a, 0x12, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x46,
	0x45, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d,
	0x46, 0x45, 0x54, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x4b,
	0x45, 0x50, 0x50, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x45, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x47, 0x52, 0x41, 0x54, 0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03,
	0x2a, 0x47, 0x0a, 0x13, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x43, 0x41,
	0x4c, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x46, 0x43, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x46, 0x43,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x46, 0x43,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x02, 0x2a, 0xb7, 0x01, 0x0a, 0x0d, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x12, 0x0e, 0x0a, 0x0a, 0x49,
	0x43, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49,
	0x43, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x43, 0x5f,
	0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x43, 0x5f, 0x57,
	0x45, 0x41, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x43, 0x5f,
	0x45, 0x51, 0x55, 0x49, 0x50, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x43, 0x5f, 0x54, 0x49,
	0x43, 0x4b, 0x45, 0x54, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x43, 0x5f, 0x46, 0x52, 0x41,
	0x47, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x43, 0x5f, 0x52,
	0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x43, 0x5f, 0x47, 0x4f,
	0x4f, 0x44, 0x53, 0x10, 0x09, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x0a, 0x2a, 0xbc, 0x04, 0x0a, 0x09, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59,
	0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x10, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x54, 0x5f, 0x43, 0x55,
	0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x44, 0x49, 0x41, 0x4d, 0x4f, 0x4e, 0x44, 0x10, 0x66,
	0x12, 0x16, 0x0a, 0x12, 0x49, 0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f,
	0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x10, 0x67, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x54, 0x5f, 0x43,
	0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x45, 0x58, 0x50, 0x10, 0x68, 0x12, 0x12, 0x0a,
	0x0d, 0x49, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x50, 0x52, 0x4f, 0x42, 0x45, 0x10, 0xc9,
	0x01, 0x12, 0x11, 0x0a, 0x0c, 0x49, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x46, 0x4f, 0x4f,
	0x44, 0x10, 0xca, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c,
	0x45, 0x5f, 0x52, 0x4f, 0x44, 0x53, 0x10, 0xad, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x54, 0x5f,
	0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x45, 0x6c, 0x10, 0xae, 0x02, 0x12, 0x13,
	0x0a, 0x0e, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0xaf, 0x02, 0x12, 0x15, 0x0a, 0x10, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45,
	0x5f, 0x4c, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0xb0, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x49, 0x54,
	0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x10, 0xb1, 0x02, 0x12,
	0x16, 0x0a, 0x11, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x42, 0x4f, 0x42,
	0x42, 0x45, 0x52, 0x53, 0x10, 0xb2, 0x02, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x54, 0x5f, 0x54, 0x41,
	0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x53, 0x10, 0xb3, 0x02,
	0x12, 0x14, 0x0a, 0x0f, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x48, 0x4f,
	0x4f, 0x4b, 0x53, 0x10, 0xb4, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x54, 0x5f, 0x54, 0x41, 0x43,
	0x4b, 0x4c, 0x45, 0x5f, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x10, 0xb5, 0x02, 0x12, 0x1b, 0x0a, 0x16,
	0x49, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x48, 0x45, 0x41, 0x44,
	0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10, 0x91, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x49, 0x54, 0x5f,
	0x57, 0x45, 0x41, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x56, 0x41, 0x54, 0x41, 0x52, 0x10,
	0x92, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x49, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x53,
	0x49, 0x4e, 0x4b, 0x45, 0x52, 0x53, 0x10, 0xf5, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x49, 0x54, 0x5f,
	0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56,
	0x45, 0x52, 0x53, 0x10, 0xf6, 0x03, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x54, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xd9, 0x04, 0x12, 0x16, 0x0a, 0x11,
	0x49, 0x54, 0x5f, 0x46, 0x52, 0x41, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0xbd, 0x05, 0x12, 0x12, 0x0a, 0x0d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x42, 0x41, 0x47, 0x10, 0xa1, 0x06, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x54, 0x5f, 0x47,
	0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x85, 0x07, 0x12, 0x12,
	0x0a, 0x0d, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10,
	0xe9, 0x07, 0x2a, 0x52, 0x0a, 0x0e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41,
	0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x45, 0x4b, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x45, 0x4b, 0x5f, 0x43, 0x55, 0x52,
	0x52, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x49, 0x45, 0x4b, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x10, 0x02, 0x2a, 0x79, 0x0a, 0x0c, 0x55, 0x4e, 0x53, 0x54, 0x41, 0x43,
	0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x53, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x0f, 0x55, 0x53, 0x54, 0x5f, 0x54,
	0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x52, 0x4f, 0x44, 0x53, 0x10, 0xad, 0x02, 0x12, 0x14, 0x0a,
	0x0f, 0x55, 0x53, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x45, 0x6c,
	0x10, 0xae, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x55, 0x53, 0x54, 0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c,
	0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0xaf, 0x02, 0x12, 0x16, 0x0a, 0x11, 0x55, 0x53, 0x54,
	0x5f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0xb0,
	0x02, 0x2a, 0x38, 0x0a, 0x0c, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x47, 0x10, 0x02, 0x2a, 0x3c, 0x0a, 0x0b, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x53,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x53,
	0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x53, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x66, 0x0a, 0x0e, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x12, 0x0e, 0x0a, 0x0a, 0x49,
	0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x49,
	0x4f, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4f, 0x5f, 0x52, 0x45,
	0x44, 0x55, 0x43, 0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4f, 0x5f, 0x4c, 0x4f, 0x43,
	0x4b, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4f, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45,
	0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4f, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x05, 0x2a, 0x90, 0x05, 0x0a, 0x10, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x53, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x53, 0x54, 0x5f, 0x50,
	0x41, 0x59, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x53, 0x54, 0x5f, 0x47, 0x4d, 0x10, 0x02,
	0x12, 0x13, 0x0a, 0x0f, 0x49, 0x53, 0x54, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x42, 0x4f,
	0x4e, 0x55, 0x53, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x4f,
	0x52, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x53, 0x54, 0x5f,
	0x47, 0x4d, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x16,
	0x0a, 0x12, 0x49, 0x53, 0x54, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x4e, 0x44,
	0x5f, 0x46, 0x45, 0x45, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x08, 0x12, 0x15,
	0x0a, 0x11, 0x49, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x53, 0x54, 0x5f, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x0a, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x53,
	0x54, 0x5f, 0x45, 0x58, 0x50, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x10, 0x0b,
	0x12, 0x21, 0x0a, 0x1d, 0x49, 0x53, 0x54, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x4f, 0x4e,
	0x44, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52,
	0x44, 0x10, 0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x53, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f,
	0x4e, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x0d, 0x12,
	0x1a, 0x0a, 0x16, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x0e, 0x12, 0x13, 0x0a, 0x0f, 0x49,
	0x53, 0x54, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x0f,
	0x12, 0x10, 0x0a, 0x0c, 0x49, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x4d, 0x4f, 0x56, 0x45,
	0x10, 0x10, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x58, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x10, 0x11, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x53, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d,
	0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x10, 0x12, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x53, 0x54, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x13,
	0x12, 0x14, 0x0a, 0x10, 0x49, 0x53, 0x54, 0x5f, 0x47, 0x55, 0x49, 0x44, 0x45, 0x5f, 0x52, 0x45,
	0x57, 0x41, 0x52, 0x44, 0x10, 0x14, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55,
	0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x15, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x53, 0x54,
	0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10,
	0x16, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x43, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0x17, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x53,
	0x54, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0x18, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x53, 0x54, 0x5f, 0x43,
	0x44, 0x4b, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x19, 0x12, 0x17, 0x0a, 0x13, 0x49,
	0x53, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x57, 0x41,
	0x52, 0x44, 0x10, 0x1a, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x54,
	0x5f, 0x54, 0x48, 0x52, 0x4f, 0x57, 0x5f, 0x52, 0x4f, 0x44, 0x10, 0x1b, 0x12, 0x16, 0x0a, 0x12,
	0x49, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x42,
	0x55, 0x59, 0x10, 0x1c, 0x2a, 0x61, 0x0a, 0x10, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x53,
	0x48, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x53, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x53, 0x54,
	0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x53, 0x54,
	0x5f, 0x54, 0x49, 0x50, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x53, 0x54, 0x5f, 0x44, 0x49,
	0x41, 0x4c, 0x4f, 0x47, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x53, 0x54, 0x5f, 0x43, 0x4f,
	0x4d, 0x42, 0x49, 0x4e, 0x45, 0x10, 0x04, 0x2a, 0x4c, 0x0a, 0x10, 0x42, 0x41, 0x47, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x42,
	0x4f, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x4f, 0x54,
	0x5f, 0x53, 0x41, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x4f, 0x54, 0x5f, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0x03, 0x2a, 0x83, 0x01, 0x0a, 0x11, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x51,
	0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x49,
	0x51, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x49, 0x51, 0x54, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x51, 0x54, 0x5f, 0x45, 0x50, 0x49, 0x43, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x51,
	0x54, 0x5f, 0x52, 0x41, 0x52, 0x45, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x51, 0x54, 0x5f,
	0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x51, 0x54, 0x5f,
	0x53, 0x55, 0x50, 0x45, 0x52, 0x42, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x51, 0x54, 0x5f,
	0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x41, 0x52, 0x59, 0x10, 0x06, 0x2a, 0x7e, 0x0a, 0x0c, 0x52,
	0x4f, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x11, 0x0a, 0x0d, 0x52,
	0x4f, 0x44, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x52, 0x4f, 0x44, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x52, 0x4f, 0x44, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x49, 0x4e, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x4f, 0x44, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x53,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x4f, 0x44, 0x53, 0x54, 0x5f,
	0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x4f, 0x44,
	0x53, 0x54, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x44, 0x10, 0x05, 0x2a, 0x57, 0x0a, 0x0d, 0x52,
	0x45, 0x45, 0x4c, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x12, 0x0a, 0x0e,
	0x52, 0x45, 0x45, 0x4c, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x45, 0x4c, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x49, 0x4e, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x45, 0x4c, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x53, 0x54,
	0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x45, 0x4c, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x4f,
	0x4f, 0x4c, 0x10, 0x03, 0x2a, 0x77, 0x0a, 0x0d, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x55, 0x42,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x49, 0x4e, 0x45, 0x53, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x49, 0x4e,
	0x45, 0x53, 0x54, 0x5f, 0x4e, 0x59, 0x4c, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4c,
	0x49, 0x4e, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x42, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x14,
	0x0a, 0x10, 0x4c, 0x49, 0x4e, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x45, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x49, 0x4e, 0x45, 0x53, 0x54, 0x5f, 0x46,
	0x4c, 0x55, 0x4f, 0x52, 0x4f, 0x43, 0x41, 0x52, 0x42, 0x4f, 0x4e, 0x10, 0x04, 0x2a, 0x4e, 0x0a,
	0x0f, 0x4c, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x12, 0x14, 0x0a, 0x10, 0x4c, 0x45, 0x41, 0x44, 0x45, 0x52, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x41, 0x44, 0x45, 0x52,
	0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x45,
	0x41, 0x44, 0x45, 0x52, 0x53, 0x54, 0x5f, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x2a, 0x8c, 0x02,
	0x0a, 0x0d, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12,
	0x12, 0x0a, 0x0e, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x4c, 0x49,
	0x56, 0x45, 0x10, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x44,
	0x45, 0x41, 0x44, 0x10, 0x66, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f,
	0x53, 0x45, 0x45, 0x44, 0x53, 0x10, 0x67, 0x12, 0x11, 0x0a, 0x0d, 0x42, 0x41, 0x49, 0x54, 0x53,
	0x54, 0x5f, 0x49, 0x4e, 0x53, 0x45, 0x43, 0x54, 0x10, 0x68, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x41,
	0x49, 0x54, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x48, 0x45, 0x4c,
	0x4c, 0x10, 0x69, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x10, 0x6a, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x6b, 0x12, 0x13, 0x0a, 0x0f, 0x42,
	0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x45, 0x47, 0x47, 0x10, 0x6c,
	0x12, 0x15, 0x0a, 0x11, 0x42, 0x41, 0x49, 0x54, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f,
	0x50, 0x41, 0x53, 0x54, 0x41, 0x10, 0x6d, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x41, 0x49, 0x54, 0x53,
	0x54, 0x5f, 0x47, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x6e, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x41, 0x49,
	0x54, 0x53, 0x54, 0x5f, 0x4d, 0x45, 0x41, 0x54, 0x10, 0x6f, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x41,
	0x49, 0x54, 0x53, 0x54, 0x5f, 0x44, 0x41, 0x49, 0x52, 0x59, 0x10, 0x70, 0x2a, 0xfb, 0x02, 0x0a,
	0x0d, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x12,
	0x0a, 0x0e, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x48, 0x41,
	0x44, 0x10, 0xc9, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x47,
	0x52, 0x55, 0x42, 0x10, 0xca, 0x01, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x57, 0x4f, 0x52, 0x4d, 0x10, 0xcb, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x55, 0x52, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x48, 0x52, 0x49, 0x4d, 0x50, 0x10, 0xcc, 0x01, 0x12, 0x12, 0x0a, 0x0d,
	0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x4d, 0x49, 0x4e, 0x4e, 0x4f, 0x57, 0x10, 0xad, 0x02,
	0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x50, 0x50, 0x45,
	0x52, 0x10, 0xae, 0x02, 0x12, 0x11, 0x0a, 0x0c, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x50, 0x4f, 0x4f, 0x4e, 0x10, 0xaf, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x4c, 0x55, 0x52, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x50, 0x49, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0xb0, 0x02, 0x12, 0x0f, 0x0a, 0x0a,
	0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x56, 0x49, 0x42, 0x10, 0xb1, 0x02, 0x12, 0x13, 0x0a,
	0x0e, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10,
	0xb2, 0x02, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x50, 0x45, 0x4e,
	0x43, 0x49, 0x4c, 0x10, 0xb3, 0x02, 0x12, 0x11, 0x0a, 0x0c, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x4e, 0x4f, 0x53, 0x49, 0x45, 0x10, 0xb4, 0x02, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x55, 0x52,
	0x45, 0x53, 0x54, 0x5f, 0x4b, 0x4e, 0x4f, 0x54, 0x54, 0x59, 0x10, 0xb5, 0x02, 0x12, 0x10, 0x0a,
	0x0b, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x47, 0x10, 0xb6, 0x02, 0x12,
	0x16, 0x0a, 0x11, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x53, 0x53, 0x4a, 0x49,
	0x47, 0x47, 0x45, 0x52, 0x10, 0xb7, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x55, 0x52, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x50, 0x49, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x45, 0x10, 0xb8, 0x02, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x55, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x48, 0x41, 0x4b, 0x45, 0x52, 0x10, 0xb9, 0x02, 0x2a, 0x6f, 0x0a, 0x0f, 0x42, 0x4f,
	0x42, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a,
	0x0b, 0x42, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x42, 0x53, 0x54, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x42, 0x53, 0x54, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x42, 0x53, 0x54, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b,
	0x42, 0x53, 0x54, 0x5f, 0x54, 0x48, 0x52, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x04, 0x12, 0x0c, 0x0a,
	0x08, 0x42, 0x53, 0x54, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x10, 0x05, 0x2a, 0xb2, 0x01, 0x0a, 0x0e,
	0x48, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10,
	0x0a, 0x0c, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x01, 0x12,
	0x0e, 0x0a, 0x0a, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x02, 0x12,
	0x0e, 0x0a, 0x0a, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x49, 0x53, 0x45, 0x4e, 0x49, 0x10, 0x65, 0x12,
	0x0f, 0x0a, 0x0b, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x53, 0x4c, 0x45, 0x45, 0x56, 0x45, 0x10, 0x66,
	0x12, 0x13, 0x0a, 0x0e, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x48, 0x45,
	0x41, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x45, 0x59, 0x45, 0x10, 0xca, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x48, 0x4b, 0x53,
	0x54, 0x5f, 0x42, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x48, 0x41, 0x4e, 0x4b, 0x10, 0xcb, 0x01, 0x12,
	0x0f, 0x0a, 0x0a, 0x48, 0x4b, 0x53, 0x54, 0x5f, 0x57, 0x41, 0x43, 0x4b, 0x59, 0x10, 0xcc, 0x01,
	0x2a, 0x73, 0x0a, 0x0f, 0x52, 0x4f, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x54, 0x5f, 0x58, 0x53, 0x10, 0x01,
	0x12, 0x09, 0x0a, 0x05, 0x52, 0x41, 0x54, 0x5f, 0x53, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x52,
	0x41, 0x54, 0x5f, 0x4d, 0x53, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x41, 0x54, 0x5f, 0x4d,
	0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x54, 0x5f, 0x4d, 0x46, 0x10, 0x05, 0x12, 0x09,
	0x0a, 0x05, 0x52, 0x41, 0x54, 0x5f, 0x46, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x54,
	0x5f, 0x58, 0x46, 0x10, 0x07, 0x2a, 0x75, 0x0a, 0x11, 0x52, 0x4f, 0x44, 0x5f, 0x48, 0x41, 0x52,
	0x44, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x48,
	0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52,
	0x48, 0x54, 0x5f, 0x55, 0x4c, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x48, 0x54, 0x5f, 0x4c,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x48, 0x54, 0x5f, 0x4d, 0x4c, 0x10, 0x03, 0x12, 0x09,
	0x0a, 0x05, 0x52, 0x48, 0x54, 0x5f, 0x4d, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x48, 0x54,
	0x5f, 0x4d, 0x48, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x48, 0x54, 0x5f, 0x48, 0x10, 0x06,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x48, 0x54, 0x5f, 0x58, 0x48, 0x10, 0x07, 0x2a, 0x5e, 0x0a, 0x0e,
	0x48, 0x4f, 0x4f, 0x4b, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x12, 0x0f,
	0x0a, 0x0b, 0x48, 0x53, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0e, 0x0a, 0x0a, 0x48, 0x53, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x45, 0x4c, 0x59, 0x10, 0x01, 0x12,
	0x0c, 0x0a, 0x08, 0x48, 0x53, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x02, 0x12, 0x0c, 0x0a,
	0x08, 0x48, 0x53, 0x52, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x48,
	0x53, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x45, 0x43, 0x54, 0x10, 0x04, 0x2a, 0x50, 0x0a, 0x11,
	0x4c, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x53, 0x54, 0x5f, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x10,
	0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x53, 0x54, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x53, 0x54, 0x5f, 0x42, 0x49, 0x47, 0x10, 0x03, 0x2a, 0x37,
	0x0a, 0x0b, 0x55, 0x4e, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x48, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x55, 0x48, 0x54, 0x5f, 0x4e, 0x4f, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x48,
	0x54, 0x5f, 0x59, 0x45, 0x53, 0x10, 0x02, 0x2a, 0x6b, 0x0a, 0x0f, 0x42, 0x41, 0x49, 0x54, 0x5f,
	0x48, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x48,
	0x54, 0x5f, 0x53, 0x55, 0x52, 0x46, 0x41, 0x43, 0x45, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x07, 0x42,
	0x48, 0x54, 0x5f, 0x42, 0x45, 0x44, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x48, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x53, 0x45, 0x31,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x48, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x53, 0x45,
	0x32, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x48, 0x54, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x53,
	0x45, 0x33, 0x10, 0x03, 0x2a, 0x54, 0x0a, 0x10, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x53,
	0x41, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x53, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x41, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x53,
	0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x53, 0x54, 0x5f,
	0x52, 0x45, 0x44, 0x55, 0x43, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x53, 0x54, 0x5f,
	0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0x66, 0x0a, 0x10, 0x53, 0x54,
	0x4f, 0x52, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x12, 0x0f,
	0x0a, 0x0b, 0x53, 0x54, 0x52, 0x54, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x54, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x54, 0x52, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x52, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x10, 0x03, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x54, 0x52, 0x54, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x50, 0x41, 0x43, 0x4b,
	0x10, 0x04, 0x2a, 0x55, 0x0a, 0x16, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c,
	0x50, 0x45, 0x43, 0x56, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11,
	0x0a, 0x0d, 0x50, 0x45, 0x43, 0x56, 0x5f, 0x45, 0x58, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x43, 0x56, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x44, 0x0a, 0x0d, 0x54, 0x52, 0x49,
	0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42,
	0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x54,
	0x42, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x45, 0x41, 0x52, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x42, 0x54, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x10, 0x02, 0x2a,
	0x3c, 0x0a, 0x10, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x42, 0x4f, 0x5f, 0x49, 0x4e, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x54, 0x42, 0x4f, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x02, 0x2a, 0x96, 0x01,
	0x0a, 0x0c, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x52, 0x4f, 0x44, 0x5f, 0x53, 0x49, 0x54, 0x12, 0x0f,
	0x0a, 0x0b, 0x54, 0x52, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x54, 0x52, 0x53, 0x5f, 0x52, 0x4f, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x54, 0x52, 0x53, 0x5f, 0x52, 0x45, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x52,
	0x53, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x52, 0x53, 0x5f,
	0x4c, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x52, 0x53, 0x5f,
	0x42, 0x41, 0x49, 0x54, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x52, 0x53, 0x5f, 0x42, 0x4f,
	0x42, 0x42, 0x45, 0x52, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x52, 0x53, 0x5f, 0x48, 0x4f,
	0x4f, 0x4b, 0x53, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52, 0x53, 0x5f, 0x53, 0x49, 0x4e,
	0x4b, 0x45, 0x52, 0x53, 0x10, 0x08, 0x2a, 0x75, 0x0a, 0x0e, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x53,
	0x49, 0x5a, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x49, 0x5a, 0x45,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x49,
	0x5a, 0x45, 0x5f, 0x59, 0x4f, 0x55, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x49,
	0x5a, 0x45, 0x5f, 0x41, 0x44, 0x55, 0x4c, 0x54, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x49,
	0x5a, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x5a, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x49,
	0x5a, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b,
	0x53, 0x49, 0x5a, 0x45, 0x5f, 0x4c, 0x45, 0x47, 0x45, 0x4e, 0x44, 0x10, 0x05, 0x2a, 0x8a, 0x01,
	0x0a, 0x11, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x42, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x41, 0x43,
	0x4b, 0x4c, 0x45, 0x42, 0x54, 0x5f, 0x4b, 0x46, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x41,
	0x43, 0x4b, 0x4c, 0x45, 0x42, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x45, 0x43, 0x54, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x42, 0x54, 0x5f, 0x54, 0x4f, 0x52, 0x41,
	0x59, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x42, 0x54, 0x5f,
	0x59, 0x47, 0x4b, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x41, 0x43, 0x4b, 0x4c, 0x45, 0x42,
	0x54, 0x5f, 0x53, 0x49, 0x45, 0x47, 0x45, 0x4c, 0x10, 0x05, 0x2a, 0xf9, 0x03, 0x0a, 0x18, 0x54,
	0x41, 0x43, 0x4b, 0x4c, 0x45, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x49,
	0x45, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42, 0x53, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x42, 0x53,
	0x54, 0x5f, 0x52, 0x4f, 0x4c, 0x4c, 0x46, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x54, 0x42, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b,
	0x54, 0x42, 0x53, 0x54, 0x5f, 0x41, 0x54, 0x48, 0x4c, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x4d, 0x4f, 0x4e, 0x48, 0x55, 0x4e, 0x54, 0x45,
	0x52, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x52, 0x53,
	0x53, 0x46, 0x49, 0x52, 0x45, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42, 0x53, 0x54, 0x5f,
	0x45, 0x58, 0x50, 0x52, 0x49, 0x44, 0x45, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x53,
	0x54, 0x5f, 0x50, 0x4d, 0x41, 0x58, 0x53, 0x58, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42,
	0x53, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x54, 0x41, 0x53, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11,
	0x54, 0x42, 0x53, 0x54, 0x5f, 0x47, 0x52, 0x45, 0x41, 0x54, 0x48, 0x55, 0x4e, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x47, 0x49, 0x41, 0x4e,
	0x54, 0x57, 0x41, 0x56, 0x45, 0x53, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x53, 0x54,
	0x5f, 0x53, 0x48, 0x41, 0x44, 0x4f, 0x57, 0x10, 0x0b, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x42, 0x53,
	0x54, 0x5f, 0x43, 0x51, 0x42, 0x46, 0x53, 0x10, 0x0c, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42, 0x53,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x49, 0x46, 0x46, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x54,
	0x42, 0x53, 0x54, 0x5f, 0x4d, 0x41, 0x58, 0x34, 0x10, 0x0e, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x42,
	0x53, 0x54, 0x5f, 0x4e, 0x59, 0x4c, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42,
	0x53, 0x54, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x10, 0x12, 0x0d, 0x0a, 0x09,
	0x54, 0x42, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x10, 0x11, 0x12, 0x14, 0x0a, 0x10, 0x54,
	0x42, 0x53, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x43, 0x4c, 0x4f, 0x4e, 0x47, 0x10,
	0x12, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x4d, 0x41, 0x59, 0x46, 0x4c, 0x59,
	0x10, 0x13, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x53, 0x48, 0x41, 0x44, 0x10,
	0x14, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x47, 0x52, 0x55, 0x42, 0x10, 0x15,
	0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x50, 0x50, 0x45, 0x52, 0x10,
	0x16, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x4d, 0x49, 0x4e, 0x4e, 0x4f, 0x57,
	0x10, 0x17, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x4f, 0x4f, 0x4e,
	0x10, 0x18, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x53, 0x50, 0x49, 0x4e, 0x4e,
	0x45, 0x52, 0x10, 0x19, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x42, 0x53, 0x54, 0x5f, 0x43, 0x48, 0x4b,
	0x48, 0x4f, 0x4f, 0x4b, 0x10, 0x1a, 0x2a, 0x65, 0x0a, 0x08, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x54, 0x5f, 0x53, 0x41, 0x4e, 0x44, 0x42, 0x4f, 0x58,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x54, 0x5f, 0x57, 0x45, 0x43, 0x48, 0x41, 0x54, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x54, 0x5f, 0x41, 0x4c, 0x49, 0x50, 0x41, 0x59, 0x10, 0x03,
	0x12, 0x0c, 0x0a, 0x08, 0x50, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0d,
	0x0a, 0x09, 0x50, 0x54, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10, 0x05, 0x2a, 0x59, 0x0a,
	0x0e, 0x42, 0x55, 0x59, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12,
	0x0c, 0x0a, 0x08, 0x42, 0x4c, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x42, 0x4c, 0x54, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x4c,
	0x54, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x4c, 0x54, 0x5f,
	0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x4c, 0x54, 0x5f, 0x46,
	0x4f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x10, 0x04, 0x2a, 0x93, 0x01, 0x0a, 0x16, 0x50, 0x55, 0x52,
	0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x45, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x42, 0x42,
	0x59, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x45, 0x54,
	0x5f, 0x4c, 0x4f, 0x42, 0x42, 0x59, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x02, 0x12, 0x16, 0x0a,
	0x12, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x42, 0x42, 0x59, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x49, 0x54, 0x59, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x42,
	0x42, 0x59, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x54,
	0x5f, 0x4c, 0x4f, 0x42, 0x42, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x05, 0x2a, 0xfd,
	0x01, 0x0a, 0x15, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x47,
	0x47, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x54, 0x54, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x54, 0x54,
	0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x43,
	0x4b, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x54, 0x54, 0x5f, 0x4c, 0x41, 0x43, 0x4b, 0x5f,
	0x53, 0x50, 0x49, 0x4e, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x50, 0x54, 0x54, 0x5f, 0x4c, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x5f, 0x50, 0x4f,
	0x50, 0x55, 0x50, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x54, 0x54, 0x5f, 0x4c, 0x41, 0x43,
	0x4b, 0x5f, 0x44, 0x49, 0x41, 0x4d, 0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10,
	0x04, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x54, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x50,
	0x54, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50,
	0x4f, 0x50, 0x55, 0x50, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x54, 0x54, 0x5f, 0x4d, 0x4f,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x4f,
	0x50, 0x55, 0x50, 0x10, 0x07, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x54, 0x54, 0x5f, 0x54, 0x4f, 0x50,
	0x5f, 0x55, 0x50, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x08, 0x2a, 0x90,
	0x04, 0x0a, 0x14, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x2a, 0x0a, 0x26, 0x50, 0x55, 0x52, 0x43, 0x48,
	0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48,
	0x41, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c,
	0x45, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x55, 0x4e,
	0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21, 0x50,
	0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25,
	0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f,
	0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x55, 0x52, 0x43, 0x48,
	0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x07, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x12, 0x23, 0x0a,
	0x1f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54,
	0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x10,
	0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x49, 0x4e, 0x47, 0x10,
	0x0c, 0x2a, 0xc7, 0x02, 0x0a, 0x14, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x42,
	0x49, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x42,
	0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x50,
	0x42, 0x53, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x42,
	0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x42,
	0x53, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x41, 0x4e, 0x45, 0x57, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x50, 0x42, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12,
	0x50, 0x42, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x42, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49,
	0x46, 0x59, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x06,
	0x12, 0x1a, 0x0a, 0x16, 0x50, 0x42, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12,
	0x50, 0x42, 0x53, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x42, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x42, 0x53, 0x5f, 0x52,
	0x45, 0x50, 0x4c, 0x45, 0x4e, 0x49, 0x53, 0x48, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x42, 0x53, 0x5f, 0x41, 0x42, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x5f, 0x44,
	0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x42, 0x53,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x42, 0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x0d, 0x2a, 0x82, 0x08, 0x0a, 0x06,
	0x47, 0x4d, 0x5f, 0x43, 0x4d, 0x44, 0x12, 0x0e, 0x0a, 0x0a, 0x47, 0x43, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x43, 0x5f, 0x47, 0x41, 0x4d,
	0x45, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x43, 0x5f, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x47, 0x43, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4b, 0x49, 0x43,
	0x4b, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0e, 0x47, 0x43, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f,
	0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xe8, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x47, 0x43, 0x5f, 0x43,
	0x4c, 0x45, 0x41, 0x52, 0x5f, 0x57, 0x45, 0x41, 0x54, 0x48, 0x45, 0x52, 0x10, 0xe9, 0x07, 0x12,
	0x11, 0x0a, 0x0c, 0x47, 0x43, 0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x5f, 0x45, 0x4e, 0x44, 0x10,
	0xcb, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x45,
	0x47, 0x49, 0x4e, 0x10, 0xcc, 0x08, 0x12, 0x11, 0x0a, 0x0c, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c,
	0x4c, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0xcd, 0x08, 0x12, 0x1d, 0x0a, 0x18, 0x47, 0x43, 0x5f,
	0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55, 0x53, 0x5f,
	0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0xce, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x43, 0x5f, 0x48,
	0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x44, 0x4b, 0x10, 0xcf,
	0x08, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x51, 0x55, 0x45,
	0x52, 0x59, 0x5f, 0x43, 0x44, 0x4b, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x45, 0x53, 0x10, 0xd0,
	0x08, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x51, 0x55, 0x45,
	0x52, 0x59, 0x5f, 0x43, 0x44, 0x4b, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x53, 0x10, 0xd1,
	0x08, 0x12, 0x20, 0x0a, 0x1b, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43, 0x44, 0x4b, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x45, 0x53,
	0x10, 0xd2, 0x08, 0x12, 0x10, 0x0a, 0x0b, 0x47, 0x43, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x45,
	0x4e, 0x44, 0x10, 0xaf, 0x09, 0x12, 0x13, 0x0a, 0x0e, 0x47, 0x43, 0x5f, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xb0, 0x09, 0x12, 0x1c, 0x0a, 0x17, 0x47, 0x43,
	0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0xb1, 0x09, 0x12, 0x11, 0x0a, 0x0c, 0x47, 0x43, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x93, 0x0a, 0x12, 0x12, 0x0a, 0x0d, 0x47,
	0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x94, 0x0a, 0x12,
	0x14, 0x0a, 0x0f, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x45, 0x10, 0x95, 0x0a, 0x12, 0x19, 0x0a, 0x14, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x96, 0x0a,
	0x12, 0x17, 0x0a, 0x12, 0x47, 0x4d, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x97, 0x0a, 0x12, 0x10, 0x0a, 0x0b, 0x47, 0x4d, 0x5f,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xf7, 0x0a, 0x12, 0x19, 0x0a, 0x14, 0x47,
	0x4d, 0x5f, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x42, 0x45,
	0x47, 0x49, 0x4e, 0x10, 0xf8, 0x0a, 0x12, 0x1c, 0x0a, 0x17, 0x47, 0x4d, 0x5f, 0x43, 0x4d, 0x44,
	0x5f, 0x47, 0x43, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47,
	0x59, 0x10, 0xf9, 0x0a, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x4d, 0x5f, 0x43, 0x4d, 0x44, 0x5f, 0x47,
	0x4d, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xdb, 0x0b, 0x12, 0x19, 0x0a,
	0x14, 0x47, 0x4d, 0x5f, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f,
	0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0xdc, 0x0b, 0x12, 0x20, 0x0a, 0x1b, 0x47, 0x4d, 0x5f, 0x43,
	0x4d, 0x44, 0x5f, 0x47, 0x43, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x4f,
	0x4f, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0xdd, 0x0b, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x4d,
	0x5f, 0x43, 0x4d, 0x44, 0x5f, 0x47, 0x4d, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x45, 0x4e, 0x44,
	0x10, 0xbf, 0x0c, 0x12, 0x11, 0x0a, 0x0c, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x45,
	0x47, 0x49, 0x4e, 0x10, 0xc0, 0x0c, 0x12, 0x15, 0x0a, 0x10, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47,
	0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0xc1, 0x0c, 0x12, 0x15, 0x0a,
	0x10, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44, 0x43, 0x41, 0x53,
	0x54, 0x10, 0xca, 0x0c, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x41,
	0x4e, 0x4e, 0x5f, 0x50, 0x4f, 0x50, 0x5f, 0x47, 0x45, 0x54, 0x10, 0xcb, 0x0c, 0x12, 0x18, 0x0a,
	0x13, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x41, 0x4e, 0x4e, 0x5f, 0x50, 0x4f, 0x50, 0x5f,
	0x45, 0x44, 0x49, 0x54, 0x10, 0xcc, 0x0c, 0x12, 0x17, 0x0a, 0x12, 0x47, 0x43, 0x5f, 0x4d, 0x53,
	0x47, 0x5f, 0x41, 0x4e, 0x4e, 0x5f, 0x50, 0x4f, 0x50, 0x5f, 0x44, 0x45, 0x4c, 0x10, 0xcd, 0x0c,
	0x12, 0x0f, 0x0a, 0x0a, 0x47, 0x43, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xa3,
	0x0d, 0x12, 0x12, 0x0a, 0x0d, 0x47, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x45, 0x47,
	0x49, 0x4e, 0x10, 0xa4, 0x0d, 0x12, 0x1e, 0x0a, 0x19, 0x47, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x10, 0xa5, 0x0d, 0x12, 0x10, 0x0a, 0x0b, 0x47, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x45, 0x4e, 0x44, 0x10, 0x87, 0x0e, 0x12, 0x12, 0x0a, 0x0d, 0x47, 0x43, 0x5f, 0x52, 0x41,
	0x4e, 0x4b, 0x5f, 0x42, 0x45, 0x47, 0x49, 0x4e, 0x10, 0x88, 0x0e, 0x12, 0x17, 0x0a, 0x12, 0x47,
	0x43, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x46, 0x4c, 0x55, 0x53, 0x48, 0x5f, 0x52, 0x41, 0x4e,
	0x4b, 0x10, 0x89, 0x0e, 0x12, 0x18, 0x0a, 0x13, 0x47, 0x43, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f,
	0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x10, 0x8a, 0x0e, 0x12, 0x10,
	0x0a, 0x0b, 0x47, 0x43, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0xeb, 0x0e,
	0x2a, 0xc9, 0x02, 0x0a, 0x0a, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12,
	0x0e, 0x0a, 0x0a, 0x45, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x5f, 0x55, 0x50, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x54, 0x5f,
	0x49, 0x54, 0x45, 0x4d, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x0b, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x54,
	0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x52, 0x45, 0x44, 0x55, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x0f,
	0x0a, 0x0b, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x47, 0x45, 0x54, 0x10, 0x1e, 0x12,
	0x16, 0x0a, 0x12, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x57,
	0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x1f, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x10, 0x28, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x4f,
	0x55, 0x54, 0x10, 0x29, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x54, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x32, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x54,
	0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x10, 0x3c, 0x12, 0x11, 0x0a,
	0x0d, 0x45, 0x54, 0x5f, 0x4c, 0x45, 0x41, 0x56, 0x45, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x10, 0x3d,
	0x12, 0x12, 0x0a, 0x0e, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x53, 0x45, 0x54, 0x54,
	0x4c, 0x45, 0x10, 0x3e, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f,
	0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x3f, 0x12,
	0x16, 0x0a, 0x12, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c,
	0x45, 0x5f, 0x56, 0x41, 0x4c, 0x10, 0x40, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x10, 0x41, 0x2a, 0xf7, 0x05, 0x0a,
	0x0d, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f,
	0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x11, 0x0a, 0x0d, 0x45, 0x49, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x5f, 0x54, 0x53,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x49, 0x4b, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x49, 0x4b, 0x5f, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x10,
	0x03, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x44,
	0x10, 0xe9, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x49, 0x4b, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0xea, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x45,
	0x49, 0x4b, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xeb, 0x07, 0x12,
	0x14, 0x0a, 0x0f, 0x45, 0x49, 0x4b, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x10, 0xec, 0x07, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x49, 0x4b, 0x5f, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xed, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x45, 0x49,
	0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x10, 0xb9, 0x17, 0x12, 0x15,
	0x0a, 0x10, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x41, 0x54, 0x48,
	0x45, 0x52, 0x10, 0xba, 0x17, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x49, 0x44, 0x10, 0xbb, 0x17, 0x12, 0x14, 0x0a, 0x0f, 0x45, 0x49, 0x4b, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0xbc, 0x17, 0x12, 0x11, 0x0a,
	0x0c, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x52, 0x4f, 0x47, 0x10, 0xbd, 0x17,
	0x12, 0x14, 0x0a, 0x0f, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4c, 0x45, 0x4e,
	0x47, 0x54, 0x48, 0x10, 0xbe, 0x17, 0x12, 0x15, 0x0a, 0x10, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0xbf, 0x17, 0x12, 0x13, 0x0a,
	0x0e, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x47, 0x45, 0x4e, 0x55, 0x53, 0x10,
	0xc0, 0x17, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x53,
	0x4f, 0x4d, 0x41, 0x54, 0x4f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xc1, 0x17, 0x12, 0x15, 0x0a, 0x10,
	0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c,
	0x10, 0xc2, 0x17, 0x12, 0x12, 0x0a, 0x0d, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f,
	0x42, 0x41, 0x49, 0x54, 0x10, 0xc3, 0x17, 0x12, 0x14, 0x0a, 0x0f, 0x45, 0x49, 0x4b, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x42, 0x52, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0xc4, 0x17, 0x12, 0x1f, 0x0a,
	0x1a, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x47, 0x52, 0x41,
	0x54, 0x55, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0xc5, 0x17, 0x12, 0x11,
	0x0a, 0x0c, 0x45, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0xc6,
	0x17, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x49, 0x4b, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x44,
	0x41, 0x54, 0x45, 0x10, 0xa2, 0x1f, 0x12, 0x14, 0x0a, 0x0f, 0x45, 0x49, 0x4b, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0xa3, 0x1f, 0x12, 0x1b, 0x0a, 0x16,
	0x45, 0x49, 0x4b, 0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e,
	0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x85, 0x20, 0x12, 0x15, 0x0a, 0x10, 0x45, 0x49, 0x4b,
	0x5f, 0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x10, 0x86, 0x20,
	0x12, 0x10, 0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x49, 0x44, 0x10,
	0x8a, 0x27, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x49,
	0x44, 0x10, 0x8b, 0x27, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x49, 0x4b, 0x5f, 0x54, 0x52, 0x49, 0x50,
	0x5f, 0x43, 0x4f, 0x49, 0x4e, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0x8c, 0x27,
	0x12, 0x16, 0x0a, 0x11, 0x45, 0x49, 0x4b, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x56, 0x41, 0x4c, 0x10, 0x8d, 0x27, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x49, 0x4b, 0x5f,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0xf1, 0x2e, 0x12, 0x12, 0x0a, 0x0d, 0x45, 0x49,
	0x4b, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xf2, 0x2e, 0x12, 0x16,
	0x0a, 0x11, 0x45, 0x49, 0x4b, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0xf3, 0x2e, 0x2a, 0x52, 0x0a, 0x0d, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x52, 0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x53, 0x4b, 0x5f,
	0x4c, 0x4f, 0x47, 0x4f, 0x55, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x53,
	0x47, 0x5f, 0x49, 0x44, 0x53, 0x10, 0x87, 0x20, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x53, 0x4b, 0x5f,
	0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x89, 0x27, 0x2a, 0x41, 0x0a, 0x10, 0x45, 0x56,
	0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f,
	0x0a, 0x0b, 0x45, 0x55, 0x4b, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x1c, 0x0a, 0x15, 0x45, 0x55, 0x4b, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x43, 0x4f, 0x49, 0x4e,
	0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0xa0, 0xa8, 0xcd, 0x09, 0x2a, 0x87, 0x01,
	0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0x02,
	0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x52, 0x45,
	0x43, 0x4f, 0x52, 0x44, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x5f, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x5f, 0x54,
	0x52, 0x4f, 0x50, 0x48, 0x59, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x06, 0x2a, 0x64, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x53, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x53, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x4d, 0x41, 0x58, 0x10,
	0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x42, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x03, 0x2a, 0x83, 0x01,
	0x0a, 0x0b, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0e, 0x0a,
	0x0a, 0x54, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x54, 0x53, 0x5f, 0x55, 0x4e, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x53, 0x5f, 0x44, 0x55, 0x52, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49,
	0x54, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x10, 0x06, 0x2a, 0x3a, 0x0a, 0x0d, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x44, 0x44, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x41, 0x54, 0x5f, 0x41, 0x44, 0x44,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x41, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x10, 0x02, 0x2a,
	0x6a, 0x0a, 0x0d, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x43, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x54, 0x43, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x54, 0x43, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x43,
	0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x43, 0x5f, 0x41,
	0x43, 0x48, 0x49, 0x45, 0x56, 0x45, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x63, 0x46, 0x69,
	0x73, 0x68, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x10, 0x06, 0x2a, 0x48, 0x0a, 0x0d, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x58, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x50, 0x58, 0x54, 0x5f, 0x45, 0x4e, 0x43, 0x59, 0x43, 0x4c, 0x4f, 0x50, 0x45, 0x44, 0x49,
	0x41, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x58, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x42,
	0x4f, 0x4f, 0x4b, 0x10, 0x02, 0x2a, 0x28, 0x0a, 0x0f, 0x45, 0x58, 0x50, 0x52, 0x45, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x54, 0x5f, 0x4e,
	0x4f, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x54, 0x5f, 0x59, 0x45, 0x53, 0x10, 0x01, 0x2a,
	0x83, 0x01, 0x0a, 0x0b, 0x56, 0x41, 0x4c, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x12,
	0x0e, 0x0a, 0x0a, 0x56, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x56, 0x4f, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d,
	0x56, 0x4f, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12,
	0x0c, 0x0a, 0x08, 0x56, 0x4f, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x0e, 0x0a,
	0x0a, 0x56, 0x4f, 0x5f, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x10, 0x04, 0x12, 0x14, 0x0a,
	0x10, 0x56, 0x4f, 0x5f, 0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41,
	0x4c, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x51,
	0x55, 0x41, 0x4c, 0x10, 0x06, 0x2a, 0x3f, 0x0a, 0x03, 0x41, 0x4f, 0x49, 0x12, 0x0f, 0x0a, 0x0b,
	0x41, 0x4f, 0x49, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x41, 0x4f, 0x49, 0x5f, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4f,
	0x49, 0x5f, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4f, 0x49, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x52, 0x54, 0x10, 0x03, 0x2a, 0x44, 0x0a, 0x09, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f,
	0x50, 0x45, 0x4e, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x4f, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4c, 0x45,
	0x56, 0x5f, 0x47, 0x54, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x4f, 0x5f, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x40, 0x0a, 0x0a,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x54,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x54, 0x5f,
	0x54, 0x52, 0x41, 0x43, 0x45, 0x5f, 0x59, 0x45, 0x53, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x54,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x02, 0x2a, 0x6e,
	0x0a, 0x07, 0x53, 0x55, 0x4d, 0x5f, 0x41, 0x44, 0x44, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x41, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x41, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x41, 0x5f, 0x56, 0x41, 0x4c, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x41, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x03, 0x12, 0x0e, 0x0a,
	0x0a, 0x53, 0x41, 0x5f, 0x55, 0x4e, 0x49, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x53, 0x41, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x45, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x06, 0x2a, 0x4e,
	0x0a, 0x0c, 0x48, 0x4f, 0x4c, 0x49, 0x44, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x54, 0x5f, 0x48, 0x4f, 0x4c, 0x49, 0x44, 0x41, 0x59, 0x10, 0x03, 0x2a, 0x3a,
	0x0a, 0x0c, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x12, 0x0e,
	0x0a, 0x0a, 0x4d, 0x43, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x4d, 0x43, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d,
	0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x0b, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x53, 0x5f,
	0x55, 0x4e, 0x52, 0x45, 0x41, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x53, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x10, 0x03, 0x2a, 0x3c, 0x0a, 0x12, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x54, 0x54,
	0x41, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x41,
	0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x41, 0x53, 0x5f, 0x48, 0x41, 0x44, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x48,
	0x10, 0x01, 0x2a, 0x42, 0x0a, 0x18, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43,
	0x48, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x41, 0x43, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x43, 0x53, 0x5f, 0x48, 0x41, 0x44, 0x5f, 0x43,
	0x4c, 0x41, 0x49, 0x4d, 0x10, 0x01, 0x2a, 0x3b, 0x0a, 0x09, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x41,
	0x52, 0x59, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x54, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x10, 0x02, 0x2a, 0x5b, 0x0a, 0x0f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45, 0x58, 0x54, 0x45,
	0x4e, 0x44, 0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x4b, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x4b, 0x5f, 0x52,
	0x41, 0x4e, 0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x45, 0x4b, 0x5f,
	0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4d,
	0x45, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x03,
	0x2a, 0x51, 0x0a, 0x12, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44, 0x43, 0x41, 0x53,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x42, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x42, 0x5f, 0x42, 0x52, 0x4f,
	0x41, 0x44, 0x43, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x42, 0x5f, 0x54,
	0x49, 0x50, 0x53, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x42, 0x5f, 0x50, 0x4f, 0x50, 0x55,
	0x50, 0x10, 0x03, 0x2a, 0x4d, 0x0a, 0x16, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44,
	0x43, 0x41, 0x53, 0x54, 0x5f, 0x4e, 0x54, 0x46, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a,
	0x0b, 0x4d, 0x42, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x4d, 0x42, 0x4e, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x42, 0x4e, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4d, 0x4f, 0x55, 0x54, 0x48,
	0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x13, 0x4d, 0x53, 0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44, 0x43,
	0x41, 0x53, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x42, 0x45,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x42,
	0x45, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x42, 0x45, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4d, 0x4f, 0x55, 0x54, 0x48,
	0x10, 0x02, 0x2a, 0xdc, 0x01, 0x0a, 0x15, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x57, 0x41, 0x54,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x12, 0x12, 0x0a, 0x0e,
	0x55, 0x57, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x55, 0x57, 0x53, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x47, 0x52,
	0x41, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x57, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x4e, 0x45, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x57, 0x53, 0x5f, 0x44, 0x52, 0x49, 0x46,
	0x54, 0x57, 0x4f, 0x4f, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x57, 0x53, 0x5f, 0x50,
	0x49, 0x45, 0x52, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x57, 0x53, 0x5f, 0x44, 0x45, 0x45,
	0x50, 0x50, 0x49, 0x54, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x57, 0x53, 0x5f, 0x52, 0x49,
	0x44, 0x47, 0x45, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x57, 0x53, 0x5f, 0x46, 0x41, 0x55,
	0x4c, 0x54, 0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x57, 0x53, 0x5f, 0x52, 0x4f, 0x43, 0x4b,
	0x53, 0x48, 0x45, 0x4c, 0x46, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x57, 0x53, 0x5f, 0x42,
	0x41, 0x59, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x57, 0x53, 0x5f, 0x4d, 0x55, 0x44, 0x10,
	0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x57, 0x53, 0x5f, 0x47, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x10,
	0x0b, 0x2a, 0x5c, 0x0a, 0x14, 0x4d, 0x41, 0x50, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x4c,
	0x41, 0x59, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x57, 0x4c,
	0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d,
	0x57, 0x4c, 0x54, 0x5f, 0x53, 0x55, 0x52, 0x46, 0x41, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x4d, 0x57, 0x4c, 0x54, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0f,
	0x0a, 0x0b, 0x4d, 0x57, 0x4c, 0x54, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x10, 0x03, 0x2a,
	0x5a, 0x0a, 0x18, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x49, 0x43, 0x4b,
	0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x46,
	0x54, 0x4c, 0x54, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x54,
	0x4c, 0x54, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x54,
	0x4c, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x46,
	0x54, 0x4c, 0x54, 0x5f, 0x54, 0x48, 0x49, 0x52, 0x44, 0x10, 0x03, 0x2a, 0x75, 0x0a, 0x11, 0x46,
	0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x55, 0x46, 0x46, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x12, 0x0c, 0x0a, 0x08, 0x46, 0x42, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x46, 0x42, 0x54, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x43, 0x4b,
	0x10, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x42, 0x54, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x53,
	0x50, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x66, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x42, 0x54, 0x5f, 0x42,
	0x41, 0x49, 0x54, 0x5f, 0x4e, 0x4f, 0x49, 0x53, 0x45, 0x10, 0x67, 0x12, 0x15, 0x0a, 0x11, 0x46,
	0x42, 0x54, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x68, 0x2a, 0xcd, 0x01, 0x0a, 0x17, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x42,
	0x41, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x49, 0x43, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0d,
	0x0a, 0x09, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5a, 0x45, 0x4e, 0x10,
	0x02, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x46, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x53, 0x54, 0x52,
	0x41, 0x49, 0x47, 0x48, 0x54, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x42, 0x54, 0x54, 0x5f,
	0x53, 0x54, 0x52, 0x41, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x48, 0x41,
	0x4b, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x42, 0x54, 0x54, 0x5f, 0x53,
	0x48, 0x41, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x42, 0x54, 0x54,
	0x5f, 0x54, 0x57, 0x49, 0x54, 0x43, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10,
	0x46, 0x42, 0x54, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x47, 0x4f,
	0x10, 0x08, 0x2a, 0x58, 0x0a, 0x18, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41,
	0x49, 0x54, 0x5f, 0x53, 0x50, 0x4c, 0x41, 0x53, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0d,
	0x0a, 0x09, 0x46, 0x42, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x42, 0x53, 0x54, 0x5f, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x42, 0x53, 0x54, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x46, 0x42, 0x53, 0x54, 0x5f, 0x42, 0x49, 0x47, 0x10, 0x03, 0x2a, 0x56, 0x0a, 0x17,
	0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x4e, 0x4f, 0x49,
	0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4e, 0x54, 0x5f,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x42, 0x4e, 0x54, 0x5f, 0x4c,
	0x4f, 0x57, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x42, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x44,
	0x44, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4e, 0x54, 0x5f, 0x48, 0x49,
	0x47, 0x48, 0x10, 0x03, 0x2a, 0x58, 0x0a, 0x1a, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f,
	0x42, 0x41, 0x49, 0x54, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4c, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4c, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4c, 0x54, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x10, 0x02, 0x12,
	0x0d, 0x0a, 0x09, 0x46, 0x42, 0x4c, 0x54, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x10, 0x03, 0x2a, 0x3b,
	0x0a, 0x0f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x44, 0x5f, 0x4c,
	0x56, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x44, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x44, 0x4c, 0x5f, 0x4c, 0x56, 0x31, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x46, 0x44, 0x4c, 0x5f, 0x4c, 0x56, 0x32, 0x10, 0x02, 0x2a, 0x56, 0x0a, 0x13, 0x42,
	0x41, 0x54, 0x54, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x46, 0x54, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49,
	0x4c, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x46, 0x54, 0x5f, 0x42, 0x45,
	0x53, 0x54, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x46, 0x54, 0x5f, 0x47, 0x4f, 0x4f, 0x44,
	0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x46, 0x54, 0x5f, 0x44, 0x52, 0x41, 0x53, 0x54, 0x49,
	0x43, 0x10, 0x03, 0x2a, 0x4e, 0x0a, 0x10, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x52,
	0x49, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x52, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x52, 0x54, 0x5f,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x52, 0x54, 0x5f, 0x46, 0x4c,
	0x4f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x52, 0x54, 0x5f, 0x52, 0x4f, 0x43,
	0x4b, 0x10, 0x03, 0x2a, 0xa8, 0x01, 0x0a, 0x0d, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x4b, 0x45, 0x59, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x4b, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x4b, 0x5f, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x4b, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x52,
	0x49, 0x4b, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x52, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x04, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x50, 0x4f,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x49, 0x4b, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x49, 0x4b,
	0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x42, 0x52, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x07, 0x2a, 0x4e,
	0x0a, 0x0f, 0x46, 0x4c, 0x55, 0x53, 0x48, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x54, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45,
	0x4e, 0x54, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x54, 0x54, 0x5f, 0x4d, 0x4f, 0x4e, 0x54,
	0x48, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x54, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x54, 0x54, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x03, 0x2a, 0x4f,
	0x0a, 0x09, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x15, 0x0a, 0x11, 0x52,
	0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x54, 0x5f,
	0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02, 0x2a,
	0x36, 0x0a, 0x0c, 0x41, 0x4e, 0x4e, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12,
	0x0c, 0x0a, 0x08, 0x54, 0x41, 0x47, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x54, 0x41, 0x47, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x41,
	0x47, 0x5f, 0x48, 0x4f, 0x54, 0x10, 0x02, 0x2a, 0x49, 0x0a, 0x0f, 0x41, 0x4e, 0x4e, 0x5f, 0x45,
	0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x46,
	0x46, 0x45, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x45,
	0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x50, 0x41, 0x47, 0x45,
	0x10, 0x02, 0x2a, 0x5f, 0x0a, 0x10, 0x41, 0x4e, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41,
	0x59, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x50,
	0x4c, 0x41, 0x59, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44,
	0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x02,
	0x12, 0x11, 0x0a, 0x0d, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f, 0x4c, 0x4f, 0x47, 0x49,
	0x4e, 0x10, 0x03, 0x2a, 0x6c, 0x0a, 0x0d, 0x41, 0x4e, 0x4e, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x12, 0x0d, 0x0a, 0x09, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x53, 0x54, 0x4f, 0x52,
	0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x41, 0x4e, 0x4e,
	0x4f, 0x55, 0x4e, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4a,
	0x55, 0x4d, 0x50, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x4f, 0x4e, 0x44, 0x10,
	0x04, 0x2a, 0x5c, 0x0a, 0x10, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x52, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x52, 0x54, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x52, 0x54, 0x5f,
	0x4e, 0x4f, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x10, 0x02, 0x12, 0x0f,
	0x0a, 0x0b, 0x44, 0x52, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x2a,
	0x44, 0x0a, 0x10, 0x43, 0x44, 0x4b, 0x5f, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x42, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x42, 0x53, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x42, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x15, 0x43, 0x44, 0x4b, 0x5f, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x12, 0x0f,
	0x0a, 0x0b, 0x43, 0x47, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0e, 0x0a, 0x0a, 0x43, 0x47, 0x4f, 0x5f, 0x52, 0x41, 0x4e, 0x44, 0x4f, 0x4d, 0x10, 0x01, 0x12,
	0x0e, 0x0a, 0x0a, 0x43, 0x47, 0x4f, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x02, 0x2a,
	0x36, 0x0a, 0x0d, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x54, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x50, 0x55,
	0x52, 0x53, 0x55, 0x49, 0x54, 0x10, 0x01, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x2e, 0x6b,
	0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63,
	0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_enum_proto_rawDescOnce sync.Once
	file_enum_proto_rawDescData = file_enum_proto_rawDesc
)

func file_enum_proto_rawDescGZIP() []byte {
	file_enum_proto_rawDescOnce.Do(func() {
		file_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_enum_proto_rawDescData)
	})
	return file_enum_proto_rawDescData
}

var file_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 129)
var file_enum_proto_goTypes = []interface{}{
	(PLATFORM_TYPE)(0),                // 0: common.PLATFORM_TYPE
	(PRODUCT_ID)(0),                   // 1: common.PRODUCT_ID
	(CHANNEL_TYPE)(0),                 // 2: common.CHANNEL_TYPE
	(ENV_TYPE)(0),                     // 3: common.ENV_TYPE
	(NETWORK_TYPE)(0),                 // 4: common.NETWORK_TYPE
	(LANGUAGE_TYPE)(0),                // 5: common.LANGUAGE_TYPE
	(GRAY_STATUS)(0),                  // 6: common.GRAY_STATUS
	(GRAY_STRATEGY)(0),                // 7: common.GRAY_STRATEGY
	(LOCATION_TYPE)(0),                // 8: common.LOCATION_TYPE
	(FEATURE_HIDE_TYPE)(0),            // 9: common.FEATURE_HIDE_TYPE
	(WHITE_FUNC_TYPE)(0),              // 10: common.WHITE_FUNC_TYPE
	(LOGIN_TYPE)(0),                   // 11: common.LOGIN_TYPE
	(ACC_STATUS)(0),                   // 12: common.ACC_STATUS
	(ACC_TYPE)(0),                     // 13: common.ACC_TYPE
	(BAN_ACC_REASON_TYPE)(0),          // 14: common.BAN_ACC_REASON_TYPE
	(LOGIN_MODEL)(0),                  // 15: common.LOGIN_MODEL
	(LOGOUT_MODEL)(0),                 // 16: common.LOGOUT_MODEL
	(KICK_PLAYER_REASON)(0),           // 17: common.KICK_PLAYER_REASON
	(USER_NOVICE_DONE_STATUS)(0),      // 18: common.USER_NOVICE_DONE_STATUS
	(USER_INIT_INFO)(0),               // 19: common.USER_INIT_INFO
	(USER_AGE)(0),                     // 20: common.USER_AGE
	(USER_MODULE_TYPE)(0),             // 21: common.USER_MODULE_TYPE
	(RED_DOT_MAIL_SUB_TYPE)(0),        // 22: common.RED_DOT_MAIL_SUB_TYPE
	(RED_DOT_WAREHOUSE_SUB_TYPE)(0),   // 23: common.RED_DOT_WAREHOUSE_SUB_TYPE
	(RED_DOT_ACHIEVEMENT_SUB_TYPE)(0), // 24: common.RED_DOT_ACHIEVEMENT_SUB_TYPE
	(GAME_TYPE)(0),                    // 25: common.GAME_TYPE
	(ROOM_TYPE)(0),                    // 26: common.ROOM_TYPE
	(ROD_ACTION)(0),                   // 27: common.ROD_ACTION
	(ROD_STATUS)(0),                   // 28: common.ROD_STATUS
	(FISH_RESULT)(0),                  // 29: common.FISH_RESULT
	(FISH_STATUS)(0),                  // 30: common.FISH_STATUS
	(WEATHER_TYPE)(0),                 // 31: common.WEATHER_TYPE
	(WATER_AREA_TYPE)(0),              // 32: common.WATER_AREA_TYPE
	(OBSTACLE_TYPE)(0),                // 33: common.OBSTACLE_TYPE
	(DIRECTION_TYPE)(0),               // 34: common.DIRECTION_TYPE
	(FISH_ENTRY_OPT_TYPE)(0),          // 35: common.FISH_ENTRY_OPT_TYPE
	(FISH_KEEPNET_OPT_TYPE)(0),        // 36: common.FISH_KEEPNET_OPT_TYPE
	(FISH_SPECIES_TYPE)(0),            // 37: common.FISH_SPECIES_TYPE
	(FISHING_EVENT_TYPE)(0),           // 38: common.FISHING_EVENT_TYPE
	(HOOK_FISH_CALC_TYPE)(0),          // 39: common.HOOK_FISH_CALC_TYPE
	(ITEM_CATEGORY)(0),                // 40: common.ITEM_CATEGORY
	(ITEM_TYPE)(0),                    // 41: common.ITEM_TYPE
	(ITEM_EXTRA_KEY)(0),               // 42: common.ITEM_EXTRA_KEY
	(UNSTACK_TYPE)(0),                 // 43: common.UNSTACK_TYPE
	(STORAGE_TYPE)(0),                 // 44: common.STORAGE_TYPE
	(ITEM_STATUS)(0),                  // 45: common.ITEM_STATUS
	(ITEM_OPERATION)(0),               // 46: common.ITEM_OPERATION
	(ITEM_SOURCE_TYPE)(0),             // 47: common.ITEM_SOURCE_TYPE
	(REWARD_SHOW_TYPE)(0),             // 48: common.REWARD_SHOW_TYPE
	(BAG_OPERATE_TYPE)(0),             // 49: common.BAG_OPERATE_TYPE
	(ITEM_QUALITY_TYPE)(0),            // 50: common.ITEM_QUALITY_TYPE
	(ROD_SUB_TYPE)(0),                 // 51: common.ROD_SUB_TYPE
	(REEL_SUB_TYPE)(0),                // 52: common.REEL_SUB_TYPE
	(LINE_SUB_TYPE)(0),                // 53: common.LINE_SUB_TYPE
	(LEADER_SUB_TYPE)(0),              // 54: common.LEADER_SUB_TYPE
	(BAIT_SUB_TYPE)(0),                // 55: common.BAIT_SUB_TYPE
	(LURE_SUB_TYPE)(0),                // 56: common.LURE_SUB_TYPE
	(BOBBER_SUB_TYPE)(0),              // 57: common.BOBBER_SUB_TYPE
	(HOOKS_SUB_TYPE)(0),               // 58: common.HOOKS_SUB_TYPE
	(ROD_ACTION_TYPE)(0),              // 59: common.ROD_ACTION_TYPE
	(ROD_HARDNESS_TYPE)(0),            // 60: common.ROD_HARDNESS_TYPE
	(HOOKSET_RESULT)(0),               // 61: common.HOOKSET_RESULT
	(LURES_SPLASH_TYPE)(0),            // 62: common.LURES_SPLASH_TYPE
	(UNHOOK_TYPE)(0),                  // 63: common.UNHOOK_TYPE
	(BAIT_HOVER_TYPE)(0),              // 64: common.BAIT_HOVER_TYPE
	(TACKLE_SALE_TYPE)(0),             // 65: common.TACKLE_SALE_TYPE
	(STORE_SHOW_STYLE)(0),             // 66: common.STORE_SHOW_STYLE
	(POND_EVENT_CHANGE_TYPE)(0),       // 67: common.POND_EVENT_CHANGE_TYPE
	(TRIP_BAG_TYPE)(0),                // 68: common.TRIP_BAG_TYPE
	(TRIP_BAG_OPERATE)(0),             // 69: common.TRIP_BAG_OPERATE
	(TRIP_ROD_SIT)(0),                 // 70: common.TRIP_ROD_SIT
	(FISH_SIZE_TYPE)(0),               // 71: common.FISH_SIZE_TYPE
	(TACKLE_BRAND_TYPE)(0),            // 72: common.TACKLE_BRAND_TYPE
	(TACKLE_BRAND_SERIES_TYPE)(0),     // 73: common.TACKLE_BRAND_SERIES_TYPE
	(PAY_TYPE)(0),                     // 74: common.PAY_TYPE
	(BUY_LIMIT_TYPE)(0),               // 75: common.BUY_LIMIT_TYPE
	(PURCHASE_ENTRANCE_TYPE)(0),       // 76: common.PURCHASE_ENTRANCE_TYPE
	(PURCHASE_TRIGGER_TYPE)(0),        // 77: common.PURCHASE_TRIGGER_TYPE
	(PURCHASE_FAILED_TYPE)(0),         // 78: common.PURCHASE_FAILED_TYPE
	(PURCHASE_BILL_STATUS)(0),         // 79: common.PURCHASE_BILL_STATUS
	(GM_CMD)(0),                       // 80: common.GM_CMD
	(EVENT_TYPE)(0),                   // 81: common.EVENT_TYPE
	(EVENT_INT_KEY)(0),                // 82: common.EVENT_INT_KEY
	(EVENT_STR_KEY)(0),                // 83: common.EVENT_STR_KEY
	(EVENT_UNIQUE_KEY)(0),             // 84: common.EVENT_UNIQUE_KEY
	(STATS_TYPE)(0),                   // 85: common.STATS_TYPE
	(StatsSubType)(0),                 // 86: common.StatsSubType
	(TASK_STATUS)(0),                  // 87: common.TASK_STATUS
	(TASK_ADD_TYPE)(0),                // 88: common.TASK_ADD_TYPE
	(TASK_CATEGORY)(0),                // 89: common.TASK_CATEGORY
	(PROGRESS_TYPE)(0),                // 90: common.PROGRESS_TYPE
	(EXPRESSION_TYPE)(0),              // 91: common.EXPRESSION_TYPE
	(VAL_OPERATE)(0),                  // 92: common.VAL_OPERATE
	(AOI)(0),                          // 93: common.AOI
	(TASK_OPEN)(0),                    // 94: common.TASK_OPEN
	(TASK_TRACE)(0),                   // 95: common.TASK_TRACE
	(SUM_ADD)(0),                      // 96: common.SUM_ADD
	(HOLIDAY_TYPE)(0),                 // 97: common.HOLIDAY_TYPE
	(MSG_CATEGORY)(0),                 // 98: common.MSG_CATEGORY
	(MAIL_STATUS)(0),                  // 99: common.MAIL_STATUS
	(MAIL_ATTACH_STATUS)(0),           // 100: common.MAIL_ATTACH_STATUS
	(MAIL_ATTACH_CLAIM_STATUS)(0),     // 101: common.MAIL_ATTACH_CLAIM_STATUS
	(MAIL_TYPE)(0),                    // 102: common.MAIL_TYPE
	(MAIL_EXTEND_KEY)(0),              // 103: common.MAIL_EXTEND_KEY
	(MSG_BROADCAST_TYPE)(0),           // 104: common.MSG_BROADCAST_TYPE
	(MSG_BROADCAST_NTF_TYPE)(0),       // 105: common.MSG_BROADCAST_NTF_TYPE
	(MSG_BROADCAST_EVENT)(0),          // 106: common.MSG_BROADCAST_EVENT
	(UNDER_WATER_STRUCTURE)(0),        // 107: common.UNDER_WATER_STRUCTURE
	(MAP_WATER_LAYER_TYPE)(0),         // 108: common.MAP_WATER_LAYER_TYPE
	(FISHING_TRICK_LEVEL_TYPE)(0),     // 109: common.FISHING_TRICK_LEVEL_TYPE
	(FISHING_BUFF_TYPE)(0),            // 110: common.FISHING_BUFF_TYPE
	(FISHING_BAIT_TRICK_TYPE)(0),      // 111: common.FISHING_BAIT_TRICK_TYPE
	(FISHING_BAIT_SPLASH_TYPE)(0),     // 112: common.FISHING_BAIT_SPLASH_TYPE
	(FISHING_BAIT_NOISE_TYPE)(0),      // 113: common.FISHING_BAIT_NOISE_TYPE
	(FISHING_BAIT_LIGHTING_TYPE)(0),   // 114: common.FISHING_BAIT_LIGHTING_TYPE
	(FISH_DAMAGED_LV)(0),              // 115: common.FISH_DAMAGED_LV
	(BATTLING_FIGHT_TYPE)(0),          // 116: common.BATTLING_FIGHT_TYPE
	(FISHING_RIG_TYPE)(0),             // 117: common.FISHING_RIG_TYPE
	(RANK_INFO_KEY)(0),                // 118: common.RANK_INFO_KEY
	(FLUSH_TIME_TYPE)(0),              // 119: common.FLUSH_TIME_TYPE
	(RANK_TYPE)(0),                    // 120: common.RANK_TYPE
	(ANN_TAG_TYPE)(0),                 // 121: common.ANN_TAG_TYPE
	(ANN_EFFECT_TYPE)(0),              // 122: common.ANN_EFFECT_TYPE
	(ANN_DISPLAY_TYPE)(0),             // 123: common.ANN_DISPLAY_TYPE
	(ANN_JUMP_TYPE)(0),                // 124: common.ANN_JUMP_TYPE
	(DATA_REPORT_TYPE)(0),             // 125: common.DATA_REPORT_TYPE
	(CDK_BATCH_STATUS)(0),             // 126: common.CDK_BATCH_STATUS
	(CDK_GENERATION_OPTION)(0),        // 127: common.CDK_GENERATION_OPTION
	(ACTIVITY_TYPE)(0),                // 128: common.ACTIVITY_TYPE
}
var file_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_enum_proto_init() }
func file_enum_proto_init() {
	if File_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_enum_proto_rawDesc,
			NumEnums:      129,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_enum_proto_goTypes,
		DependencyIndexes: file_enum_proto_depIdxs,
		EnumInfos:         file_enum_proto_enumTypes,
	}.Build()
	File_enum_proto = out.File
	file_enum_proto_rawDesc = nil
	file_enum_proto_goTypes = nil
	file_enum_proto_depIdxs = nil
}
