# Google SDK - iOS登录403错误解决方案

## 问题描述

在iOS Google登录中，虽然能够成功交换auth code获取access token，但在调用Google Play Games API获取用户信息时出现403错误：

```
googleapi: Error 403: Request had insufficient authentication scopes.
Details: ACCESS_TOKEN_SCOPE_INSUFFICIENT
Reason: insufficientPermissions, Message: Insufficient Permission
```

## 问题原因

1. **Scope权限不足**：Google Play Games API需要特定的scope权限 `https://www.googleapis.com/auth/games`
2. **iOS端配置问题**：iOS端在请求授权时没有包含足够的权限范围
3. **API选择问题**：对于基础用户信息，不一定需要使用Play Games API

## 解决方案

### 1. 后端修复（已实现）

我们在后端实现了降级策略：

```go
// 首先尝试获取Play Games玩家信息
playGamesInfo, err := getPlayGamesPlayerInfoViaAPI(ctx, httpClient)
if err != nil {
    // 如果Play Games API失败，尝试使用基础的Google用户信息API
    basicUserInfo, fallbackErr := getBasicUserInfoViaAPI(ctx, httpClient)
    if fallbackErr != nil {
        return nil, fmt.Errorf("both APIs failed")
    }
    // 使用基础用户信息
    return createUserInfoFromBasic(basicUserInfo), nil
}
```

### 2. iOS端修复建议

在iOS端配置Google登录时，需要请求正确的scope：

#### 方案A：添加Games scope（推荐用于游戏应用）

```swift
// 在iOS端配置Google登录时添加Games scope
guard let signIn = GIDSignIn.sharedInstance else { return }

// 配置scope
signIn.configuration?.scopes = [
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/userinfo.email", 
    "https://www.googleapis.com/auth/games"  // 添加这个scope
]
```

#### 方案B：使用基础scope（适用于一般应用）

```swift
// 只使用基础用户信息scope
signIn.configuration?.scopes = [
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/userinfo.email"
]
```

### 3. Google Cloud Console配置

确保在Google Cloud Console中：

1. **启用API**：
   - Google+ API（已弃用，但某些旧配置可能需要）
   - Google Play Games Services API（如果使用Games功能）
   - Google OAuth2 API

2. **配置OAuth同意屏幕**：
   - 添加必要的scope权限
   - 确保应用状态为"已发布"或"测试中"

3. **配置OAuth 2.0客户端ID**：
   - 确保iOS bundle ID正确配置
   - 添加正确的重定向URI

## API对比

### Google Play Games API
- **优点**：提供游戏相关的丰富信息（玩家ID、游戏标签等）
- **缺点**：需要特定scope权限，配置复杂
- **适用场景**：游戏应用

### Google基础用户信息API
- **优点**：权限要求简单，配置容易
- **缺点**：信息相对基础
- **适用场景**：一般应用

## 测试验证

### 1. 检查access token的scope

可以使用Google的token info API检查当前token包含的scope：

```bash
curl "https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=YOUR_ACCESS_TOKEN"
```

### 2. 测试不同API

```bash
# 测试基础用户信息API
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     "https://www.googleapis.com/oauth2/v2/userinfo"

# 测试Play Games API
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     "https://games.googleapis.com/games/v1/players/me"
```

## 当前实现状态

✅ **后端降级策略**：已实现，会自动尝试两种API
✅ **错误处理**：详细的错误日志和降级逻辑
✅ **用户信息映射**：统一的用户信息结构

## 建议的完整解决流程

1. **立即解决**：使用当前的后端降级策略，应该能够正常获取用户信息
2. **iOS端优化**：根据应用需求选择合适的scope配置
3. **长期优化**：如果是游戏应用，建议配置完整的Games API权限

## 常见错误和解决方案

### 错误1：403 Insufficient Permission
- **原因**：scope权限不足
- **解决**：使用基础API或添加正确的scope

### 错误2：401 Invalid Credentials
- **原因**：token过期或无效
- **解决**：重新获取token

### 错误3：400 Invalid Request
- **原因**：请求参数错误
- **解决**：检查API调用格式

## 监控和日志

当前实现包含详细的日志记录：

```
INFO: Successfully exchanged auth code for access token
WARN: getPlayGamesPlayerInfoViaAPI error (trying fallback)
INFO: Successfully got user info via basic Google API
```

通过这些日志可以监控API使用情况和成功率。
