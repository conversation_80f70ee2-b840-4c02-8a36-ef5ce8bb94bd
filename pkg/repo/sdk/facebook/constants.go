package facebook

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const (
	Host        = "https://graph.facebook.com" // 正确的Facebook Graph API域名
	Version     = "v19.0"                      // 使用最新的稳定版本
	URLPre      = Host + "/" + Version + "/"
	UserInfoURL = Host + "/" + Version + "/" + "me"
	FriendURL   = Host + "/" + Version + "/" + "me/friends"
	FinderURL   = Host + "/" + Version + "/" + "me/apprequests"
	PictureURL  = "/picture" // URLPre+1036709610527151 /picture
)

const GetFinderLimit = "10" // 拉取接口的limit参数

const (
	fbClientSecret_1001 = "0e3b3ffb0d387e5de593b7e5352d26a9"
	fbClientSecret_1002 = "0e3b3ffb0d387e5de593b7e5352d26a9"
)

// GenerateAppSecretProof 生成应用密钥证明
func GenerateAppSecretProof(channelID commonPB.CHANNEL_TYPE, accessToken string) string {
	key := []byte(FacebookChannelKeyMap[channelID])
	h := hmac.New(sha256.New, key)
	h.Write([]byte(accessToken))
	return hex.EncodeToString(h.Sum(nil))
}

// 定义一个map 不同渠道不同密钥
var FacebookChannelKeyMap = map[commonPB.CHANNEL_TYPE]string{
	commonPB.CHANNEL_TYPE_CT_MASTER: fbClientSecret_1001,
	commonPB.CHANNEL_TYPE_CT_GOOGLE: fbClientSecret_1002,
}
